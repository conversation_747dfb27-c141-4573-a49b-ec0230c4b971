<?php

namespace App\Helpers;

use App\Domains\Log;
use App\Factories\LogFactory;
use App\Repositories\LogRepository;

class DBLog
{
    public static function log(
        ?string $message = "",
        ?string $from = null,
        ?int $organization_id = null,
        ?int $user_id = null,
        null|array|string $log = [],
    ) : void {
        if(is_array($log)) {
            $log = json_encode($log);
        }
        $log = new Log(
            null,
            $organization_id,
            $user_id,
            false,
            $from,
            $message,
            json_encode($log),
        );

        $logRepository = new LogRepository(
            new LogFactory()
        );

        $logRepository->store($log);
    }

    public static function logError(
        ?string $message = "",
        ?string $from = null,
        ?int $organization_id = null,
        ?int $user_id = null,
        null|array|string $log = []
    ) : void {
        if(is_array($log)) {
            $log = json_encode($log);
        }
        $log = new Log(
            null,
            $organization_id,
            $user_id,
            true,
            $from,
            $message,
            $log,
        );

        $logRepository = new LogRepository(
            new LogFactory()
        );

        $logRepository->store($log);
    }
}
