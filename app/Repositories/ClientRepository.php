<?php

namespace App\Repositories;

use App\Domains\Filters\ClientFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Client as ClientDomain;
use App\Factories\Inventory\ClientFactory;
use App\Models\Client;
use EloquentBuilder;

class ClientRepository
{
    private ClientFactory $clientFactory;

    public function __construct(ClientFactory $clientFactory){
        $this->clientFactory = $clientFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(ClientFilters $filters, OrderBy $orderBy) : array {
        $clients = [];

        $models = EloquentBuilder::to(Client::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $clients[] = $this->clientFactory->buildFromModel($model);
        }

        return [
            'data' => $clients,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, ClientFilters $filters, OrderBy $orderBy) : array {
        $clients = [];

        $models = EloquentBuilder::to(Client::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $clients[] = $this->clientFactory->buildFromModel($model);
        }

        return [
            'data' => $clients,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, ClientFilters $filters): int {
        return EloquentBuilder::to(Client::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, ClientFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Client::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(ClientDomain $client) : ClientDomain {
        $savedClient = Client::create($client->toStoreArray());

        $client->id = $savedClient->id;

        return $client;
    }

    public function update(ClientDomain $client, int $organization_id) : ClientDomain {
        Client::where('id', $client->id)
            ->where('organization_id', $organization_id)
            ->update($client->toUpdateArray());

        return $client;
    }

    public function fetchById(int $id) : ClientDomain {
        return $this->clientFactory->buildFromModel(
            Client::findOrFail($id)
        );
    }

    public function delete(ClientDomain $client) : bool {
        return Client::find($client->id)->delete();
    }
}
