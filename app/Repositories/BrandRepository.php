<?php

namespace App\Repositories;

use App\Domains\Filters\BrandFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Brand as BrandDomain;
use App\Factories\Inventory\BrandFactory;
use App\Models\Brand;
use EloquentBuilder;

class BrandRepository
{
    private BrandFactory $brandFactory;

    public function __construct(BrandFactory $brandFactory){
        $this->brandFactory = $brandFactory;
    }

    /**
     * @return array
     */
        public function fetchAll(BrandFilters $filters, OrderBy $orderBy) : array {
        $brands = [];

        $models = EloquentBuilder::to(Brand::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $brands[] = $this->brandFactory->buildFromModel($model);
        }

        return [
            'data' => $brands,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, BrandFilters $filters, OrderBy $orderBy) : array {
        $brands = [];

        $models = EloquentBuilder::to(Brand::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $brands[] = $this->brandFactory->buildFromModel($model);
        }

        return [
            'data' => $brands,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, BrandFilters $filters): int {
        return EloquentBuilder::to(Brand::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, BrandFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Brand::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(BrandDomain $brand) : BrandDomain {
        $savedBrand = Brand::create($brand->toStoreArray());

        $brand->id = $savedBrand->id;

        return $brand;
    }

    public function update(BrandDomain $brand, int $organization_id) : BrandDomain {
        Brand::where('id', $brand->id)
            ->where('organization_id', $organization_id)
            ->update($brand->toUpdateArray());

        return $brand;
    }

    public function fetchById(int $id) : BrandDomain {
        return $this->brandFactory->buildFromModel(
            Brand::findOrFail($id)
        );
    }

    public function delete(BrandDomain $brand) : bool {
        return Brand::find($brand->id)->delete();
    }
}
