<?php

namespace App\Repositories;

use App\Domains\ChatBot\Message as MessageDomain;
use App\Domains\Filters\MessageFilters;
use App\Domains\Filters\OrderBy;
use App\Enums\MessageStatus;
use App\Factories\ChatBot\MessageFactory;
use App\Models\Message;
use EloquentBuilder;

class MessageRepository
{
    private MessageFactory $messageFactory;

    public function __construct(MessageFactory $messageFactory){
        $this->messageFactory = $messageFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(MessageFilters $filters, OrderBy $orderBy) : array {
        $messages = [];

        $models = EloquentBuilder::to(Message::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $messages[] = $this->messageFactory->buildFromModel($model);
        }

        return [
            'data' => $messages,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, MessageFilters $filters, OrderBy $orderBy) : array {
        $messages = [];

        $models = EloquentBuilder::to(Message::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $messages[] = $this->messageFactory->buildFromModel($model);
        }

        return [
            'data' => $messages,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, MessageFilters $filters): int {
        return EloquentBuilder::to(Message::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, MessageFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Message::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(MessageDomain $message) : MessageDomain {
        $savedMessage = Message::create($message->toStoreArray());

        $message->id = $savedMessage->id;

        return $message;
    }

    public function update(MessageDomain $message, int $organization_id) : MessageDomain {
        Message::where('id', $message->id)
            ->where('organization_id', $organization_id)
            ->update($message->toUpdateArray());

        return $message;
    }

    public function save(MessageDomain $message, int $organization_id) : MessageDomain {
        if($message->id){
            return $this->update($message, $organization_id);
        }
        return $this->store($message);
    }

    public function fetchById(int $id) : MessageDomain {
        return $this->messageFactory->buildFromModel(
            Message::findOrFail($id)
        );
    }

    public function fetchByBot(string $bot) : MessageDomain {
        return $this->messageFactory->buildFromModel(
            Message::where('bot', $bot)->first()
        );
    }

    public function delete(MessageDomain $message) : bool {
        return Message::find($message->id)->delete();
    }

    /**
     * Fetch all messages available to send across organizations
     *
     * @param int $limit
     * @return array
     */
    public function fetchAllAvailableToSend(int $limit) : array {
        $messages = [];

        $models = Message::with(['client', 'template'])
            ->where("status", MessageStatus::is_sending->value)
            ->where("is_sent", false)
            ->where( function ($query) {
                $query->whereNull("scheduled_at")->orWhere("scheduled_at", "<=", now());
            })
            ->orderBy("created_at", "ASC")
            ->limit($limit)
            ->get();

        foreach ($models as $model){
            $messages[] = $this->messageFactory->buildFromModel($model, true);
        }

        return $messages;
    }
}
