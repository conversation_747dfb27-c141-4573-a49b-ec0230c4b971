<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\LogFilters;
use App\Domains\Log as LogDomain;
use App\Factories\LogFactory;
use App\Models\Log;
use EloquentBuilder;

class LogRepository
{
    private LogFactory $logFactory;

    public function __construct(LogFactory $logFactory){
        $this->logFactory = $logFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(LogFilters $filters, OrderBy $orderBy) : array {
        $logs = [];

        $models = EloquentBuilder::to(Log::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $logs[] = $this->logFactory->buildFromModel($model);
        }

        return [
            'data' => $logs,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, LogFilters $filters, OrderBy $orderBy) : array {
        $logs = [];

        $models = EloquentBuilder::to(Log::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $logs[] = $this->logFactory->buildFromModel($model);
        }

        return [
            'data' => $logs,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }


    public function store(LogDomain $log) : LogDomain {
        $savedLog = Log::create($log->toStoreArray());

        $log->id = $savedLog->id;

        return $log;
    }


    public function fetchById(int $id) : LogDomain {
        return $this->logFactory->buildFromModel(
            Log::with('products')
                ->with('client')
                ->findOrFail($id)
        );
    }

    public function delete(LogDomain $log) : bool {
        return Log::find($log->id)->delete();
    }

}
