<?php

namespace App\Repositories;

use App\Domains\ChatBot\Step as StepDomain;
use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StepFilters;
use App\Factories\ChatBot\StepFactory;
use App\Models\Step;
use EloquentBuilder;

class StepRepository
{
    private StepFactory $stepFactory;

    public function __construct(StepFactory $stepFactory){
        $this->stepFactory = $stepFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(StepFilters $filters, OrderBy $orderBy) : array {
        $steps = [];

        $models = EloquentBuilder::to(Step::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $steps[] = $this->stepFactory->buildFromModel($model);
        }

        return [
            'data' => $steps,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, StepFilters $filters, OrderBy $orderBy) : array {
        $steps = [];

        $models = EloquentBuilder::to(Step::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $steps[] = $this->stepFactory->buildFromModel($model);
        }

        return [
            'data' => $steps,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, StepFilters $filters): int {
        return EloquentBuilder::to(Step::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, StepFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Step::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(StepDomain $step) : StepDomain {
        $savedStep = Step::create($step->toStoreArray());

        $step->id = $savedStep->id;

        return $step;
    }

    public function update(StepDomain $step, int $organization_id) : StepDomain {
        Step::where('id', $step->id)
            ->where('organization_id', $organization_id)
            ->update($step->toUpdateArray());

        return $step;
    }

    public function save(StepDomain $step, int $organization_id) : StepDomain {
        if($step->id){
            return $this->update($step, $organization_id);
        }
        return $this->store($step);
    }

    public function fetchById(int $id) : StepDomain {
        return $this->stepFactory->buildFromModel(
            Step::findOrFail($id)
        );
    }

    public function fetchByBot(string $bot) : StepDomain {
        return $this->stepFactory->buildFromModel(
            Step::where('bot', $bot)->first()
        );
    }

    public function delete(StepDomain $step) : bool {
        return Step::find($step->id)->delete();
    }
}
