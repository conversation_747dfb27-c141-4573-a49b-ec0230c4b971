<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockFilters;
use App\Domains\Inventory\Stock as StockDomain;
use App\Factories\Inventory\StockFactory;
use App\Models\Stock;
use EloquentBuilder;

class StockRepository
{
    private StockFactory $stockFactory;

    public function __construct(StockFactory $stockFactory){
        $this->stockFactory = $stockFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(StockFilters $filters, OrderBy $orderBy) : array {
        $stocks = [];

        $models = EloquentBuilder::to(Stock::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $stocks[] = $this->stockFactory->buildFromModel($model);
        }

        return [
            'data' => $stocks,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, StockFilters $filters, OrderBy $orderBy) : array {
        $stocks = [];

        $models = EloquentBuilder::to(Stock::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $stocks[] = $this->stockFactory->buildFromModel($model);
        }

        return [
            'data' => $stocks,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, StockFilters $filters): int {
        return EloquentBuilder::to(Stock::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, StockFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Stock::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function fetchByProductId(int $product_id) : ?StockDomain {
        return $this->stockFactory->buildFromModel(
            Stock::where("product_id", $product_id)->first()
        );
    }

    public function store(StockDomain $stock) : StockDomain {
        $savedStock = Stock::create($stock->toStoreArray());

        $stock->id = $savedStock->id;

        return $stock;
    }

    public function update(StockDomain $stock, int $organization_id) : StockDomain {
        Stock::where('id', $stock->id)
            ->where('organization_id', $organization_id)
            ->update($stock->toUpdateArray());

        return $stock;
    }

    public function fetchById(int $id) : StockDomain {
        return $this->stockFactory->buildFromModel(
            Stock::findOrFail($id)
        );
    }

    public function delete(StockDomain $stock) : bool {
        return Stock::find($stock->id)->delete();
    }
}
