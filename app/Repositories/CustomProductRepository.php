<?php

namespace App\Repositories;

use App\Domains\Inventory\CustomProduct as CustomProductDomain;
use App\Factories\Inventory\CustomProductFactory;
use App\Models\CustomProduct;

class CustomProductRepository
{
    private CustomProductFactory $projectProductFactory;

    public function __construct(CustomProductFactory $projectProductFactory){
        $this->projectProductFactory = $projectProductFactory;
    }

    /**
     * @return array
     */
    public function fetchAll() : array {
        $projectProducts = [];

        $models = CustomProduct::with('project')->with('product')->paginate(30);

        foreach ($models as $model){
            $projectProducts[] = $this->projectProductFactory->buildFromModel($model);
        }

        return [
            'data' => $projectProducts,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id) : array {
        $projectProducts = [];

        $models = CustomProduct::whereHas("project", function($query) use ($organization_id){
            $query->where("projects.organization_id", $organization_id);
        })->orWhereHas("budget", function($query) use ($organization_id){
            $query->where("budgets.organization_id", $organization_id);
        })->with('project')->with('budget')->paginate(30);

        foreach ($models as $model){
            $projectProducts[] = $this->projectProductFactory->buildFromModel($model);
        }

        return [
            'data' => $projectProducts,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return CustomProductDomain[]
     */
    public function fetchFromCustom(int $project_id) : array {
        $projectProducts = [];

        $models = CustomProduct::with('project')
            ->with('product')
            ->where('project_id', $project_id)
            ->get();

        foreach ($models as $model){
            $projectProducts[] = $this->projectProductFactory->buildFromModel($model);
        }

        return $projectProducts;
    }

    public function store(CustomProductDomain $projectProduct) : CustomProductDomain {
        $savedCustomProduct = CustomProduct::create($projectProduct->toStoreArray());

        $projectProduct->id = $savedCustomProduct->id;

        return $projectProduct;
    }

    public function update(CustomProductDomain $projectProduct) : CustomProductDomain {
        CustomProduct::where('id', $projectProduct->id)
            ->update($projectProduct->toUpdateArray());

        return $projectProduct;
    }

    public function fetchById(int $id) : CustomProductDomain {
        return $this->projectProductFactory->buildFromModel(
            CustomProduct::with('project')
                ->with('budget')
                ->findOrFail($id)
        );
    }

    public function delete(CustomProductDomain $projectProduct) : bool {
        return CustomProduct::find($projectProduct->id)->delete();
    }
}
