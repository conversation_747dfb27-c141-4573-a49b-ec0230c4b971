<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\UserFilters;
use App\Domains\User as UserDomain;
use App\Factories\UserFactory;
use App\Models\User;
use EloquentBuilder;
use Illuminate\Support\Collection;

class UserRepository
{
    private UserFactory $userFactory;

    public function __construct(UserFactory $userFactory){
        $this->userFactory = $userFactory;
    }

    public function store(UserDomain $user) : UserDomain {
        $savedUser = User::create($user->toStoreArray());

        $user->id = $savedUser->id;

        return $user;
    }

    public function fetchById(int $id) : UserDomain {
        return $this->userFactory->buildFromModel(
            User::findOrFail($id)
        );
    }

    public function update(UserDomain $user, int $organization_id) : UserDomain {
        User::where('id', $user->id)
            ->where('organization_id', $organization_id)
            ->update($user->toUpdateArray());

        return $user;
    }

    public function fetchByEmail(string $email, ?int $organization_id = null) : UserDomain {
        if ($organization_id){
            return $this->userFactory->buildFromModel(
                User::where("email", $email)->where('organization_id', $organization_id)->first()
            );
        }
        return $this->userFactory->buildFromModel(
            User::where("email", $email)->first()
        );
    }

    public function fetchAllByEmail(string $email) : Collection {
        return User::where("email", $email)->get();
    }

    public function fetchByEmailCount(string $email) : int {
        return User::where("email", $email)->count();
    }

    public function delete(UserDomain $user) : bool {
        return User::find($user->id)->delete();
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, UserFilters $filters, OrderBy $orderBy) : array {
        $users = [];

        $models = EloquentBuilder::to(User::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $users[] = $this->userFactory->buildFromModel($model);
        }

        return [
            'data' => $users,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, UserFilters $filters): int {
        return EloquentBuilder::to(User::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, UserFilters $filters, string $column): float|int {
        return EloquentBuilder::to(User::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

}
