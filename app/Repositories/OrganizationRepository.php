<?php

namespace App\Repositories;

use App\Domains\Organization as OrganizationDomain;
use App\Factories\OrganizationFactory;
use App\Models\Organization;

class OrganizationRepository
{
    private OrganizationFactory $organizationFactory;

    public function __construct(OrganizationFactory $organizationFactory){
        $this->organizationFactory = $organizationFactory;
    }

    /**
     * @return array
     */
    public function fetchAll() : array {
        $organizations = [];

        $models = Organization::paginate(30);

        foreach ($models as $model){
            $organizations[] = $this->organizationFactory->buildFromModel($model);
        }

        return [
            'data' => $organizations,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function store(OrganizationDomain $organization) : OrganizationDomain {
        $savedOrganization = Organization::create($organization->toStoreArray());

        $organization->id = $savedOrganization->id;

        return $organization;
    }

    public function update(OrganizationDomain $organization) : OrganizationDomain {
        Organization::where('id', $organization->id)
            ->update($organization->toUpdateArray());

        return $organization;
    }

    public function fetchById(int $id) : OrganizationDomain {
        return $this->organizationFactory->buildFromModel(
            Organization::findOrFail($id)
        );
    }

    public function delete(OrganizationDomain $organization) : bool {
        return Organization::find($organization->id)->delete();
    }
}
