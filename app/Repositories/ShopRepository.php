<?php

namespace App\Repositories;

use App\Domains\Filters\ShopFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Shop as ShopDomain;
use App\Factories\Inventory\ShopFactory;
use App\Models\Shop;
use EloquentBuilder;

class ShopRepository
{
    private ShopFactory $shopFactory;

    public function __construct(ShopFactory $shopFactory){
        $this->shopFactory = $shopFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(ShopFilters $filters, OrderBy $orderBy) : array {
        $shops = [];

        $models = EloquentBuilder::to(Shop::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);


        foreach ($models as $model){
            $shops[] = $this->shopFactory->buildFromModel($model);
        }

        return [
            'data' => $shops,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, ShopFilters $filters, OrderBy $orderBy) : array {
        $shops = [];

        $models = EloquentBuilder::to(Shop::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $shops[] = $this->shopFactory->buildFromModel($model);
        }

        return [
            'data' => $shops,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, ShopFilters $filters): int {
        return EloquentBuilder::to(Shop::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, ShopFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Shop::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(ShopDomain $shop) : ShopDomain {
        $savedShop = Shop::create($shop->toStoreArray());

        $shop->id = $savedShop->id;

        return $shop;
    }

    public function update(ShopDomain $shop, int $organization_id) : ShopDomain {
        Shop::where('id', $shop->id)
            ->where('organization_id', $organization_id)
            ->update($shop->toUpdateArray());

        return $shop;
    }

    public function fetchById(int $id) : ShopDomain {
        return $this->shopFactory->buildFromModel(
            Shop::findOrFail($id)
        );
    }

    public function delete(ShopDomain $shop) : bool {
        return Shop::find($shop->id)->delete();
    }
}
