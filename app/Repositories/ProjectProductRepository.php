<?php

namespace App\Repositories;

use App\Domains\Inventory\ProjectProduct as ProjectProductDomain;
use App\Factories\Inventory\ProjectProductFactory;
use App\Models\ProjectProduct;

class ProjectProductRepository
{
    private ProjectProductFactory $projectProductFactory;

    public function __construct(ProjectProductFactory $projectProductFactory){
        $this->projectProductFactory = $projectProductFactory;
    }

    /**
     * @return array
     */
    public function fetchAll() : array {
        $projectProducts = [];

        $models = ProjectProduct::with('project')->with('product')->paginate(30);

        foreach ($models as $model){
            $projectProducts[] = $this->projectProductFactory->buildFromModel($model);
        }

        return [
            'data' => $projectProducts,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id) : array {
        $projectProducts = [];

        $models = ProjectProduct::whereHas("product", function($query) use ($organization_id){
            $query->where("products.organization_id", $organization_id);
        })->with('project')->with('product')->paginate(30);

        foreach ($models as $model){
            $projectProducts[] = $this->projectProductFactory->buildFromModel($model);
        }

        return [
            'data' => $projectProducts,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return ProjectProductDomain[]
     */
    public function fetchFromProject(int $project_id) : array {
        $projectProducts = [];

        $models = ProjectProduct::with('project')
            ->with('product')
            ->where('project_id', $project_id)
            ->get();

        foreach ($models as $model){
            $projectProducts[] = $this->projectProductFactory->buildFromModel($model);
        }

        return $projectProducts;
    }

    public function store(ProjectProductDomain $projectProduct) : ProjectProductDomain {
        $savedProjectProduct = ProjectProduct::create($projectProduct->toStoreArray());

        $projectProduct->id = $savedProjectProduct->id;

        return $projectProduct;
    }

    public function update(ProjectProductDomain $projectProduct, int $organization_id) : ProjectProductDomain {
        ProjectProduct::where('id', $projectProduct->id)
            ->where('organization_id', $organization_id)
            ->update($projectProduct->toUpdateArray());

        return $projectProduct;
    }

    public function fetchById(int $id) : ProjectProductDomain {
        return $this->projectProductFactory->buildFromModel(
            ProjectProduct::with('project')
                ->with('product')
                ->findOrFail($id)
        );
    }

    public function delete(ProjectProductDomain $projectProduct) : bool {
        return ProjectProduct::find($projectProduct->id)->delete();
    }
}
