<?php

namespace App\Repositories;

use App\Domains\Inventory\ProductHistory as ProductHistoryDomain;
use App\Factories\Inventory\ProductHistoryFactory;
use App\Models\ProductHistory;

class ProductHistoryRepository
{
    private ProductHistoryFactory $productHistoryFactory;

    public function __construct(ProductHistoryFactory $productHistoryFactory){
        $this->productHistoryFactory = $productHistoryFactory;
    }

    /**
     * @return array
     */
    public function fetchAll() : array {
        $productHistories = [];

        $models = ProductHistory::paginate(30);

        foreach ($models as $model){
            $productHistories[] = $this->productHistoryFactory->buildFromModel($model);
        }

        return [
            'data' => $productHistories,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id) : array {
        $productHistories = [];

        $models = ProductHistory::whereHas("product", function($query) use ($organization_id){
            $query->where("products.organization_id", $organization_id);
        })->paginate(30);

        foreach ($models as $model){
            $productHistories[] = $this->productHistoryFactory->buildFromModel($model);
        }

        return [
            'data' => $productHistories,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function store(ProductHistoryDomain $productHistory) : ProductHistoryDomain {
        $savedProductHistory = ProductHistory::create($productHistory->toStoreArray());

        $productHistory->id = $savedProductHistory->id;

        return $productHistory;
    }

    public function update(ProductHistoryDomain $productHistory, int $organization_id) : ProductHistoryDomain {
        ProductHistory::where('id', $productHistory->id)
            ->where('organization_id', $organization_id)
            ->update($productHistory->toUpdateArray());

        return $productHistory;
    }

    public function fetchById(int $id) : ProductHistoryDomain {
        return $this->productHistoryFactory->buildFromModel(
            ProductHistory::findOrFail($id)
        );
    }

    public function delete(ProductHistoryDomain $productHistory) : bool {
        return ProductHistory::find($productHistory->id)->delete();
    }
}
