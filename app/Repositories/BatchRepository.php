<?php

namespace App\Repositories;

use App\Domains\Filters\BatchFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Batch as BatchDomain;
use App\Factories\Inventory\BatchFactory;
use App\Models\Batch;
use EloquentBuilder;

class BatchRepository
{
    private BatchFactory $batchFactory;

    public function __construct(BatchFactory $batchFactory){
        $this->batchFactory = $batchFactory;
    }

    /**
     * @return array
     */
        public function fetchAll(BatchFilters $filters, OrderBy $orderBy) : array {
        $batches = [];

        $models = EloquentBuilder::to(Batch::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $batches[] = $this->batchFactory->buildFromModel($model);
        }

        return [
            'data' => $batches,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, BatchFilters $filters, OrderBy $orderBy) : array {
        $batches = [];

        $models = EloquentBuilder::to(Batch::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $batches[] = $this->batchFactory->buildFromModel($model);
        }

        return [
            'data' => $batches,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, BatchFilters $filters): int {
        return EloquentBuilder::to(Batch::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, BatchFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Batch::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(BatchDomain $batch) : BatchDomain {
        $savedBatch = Batch::create($batch->toStoreArray());

        $batch->id = $savedBatch->id;

        return $batch;
    }

    public function update(BatchDomain $batch, int $organization_id) : BatchDomain {
        Batch::where('id', $batch->id)
            ->where('organization_id', $organization_id)
            ->update($batch->toUpdateArray());

        return $batch;
    }

    public function fetchById(int $id) : BatchDomain {
        return $this->batchFactory->buildFromModel(
            Batch::findOrFail($id)
        );
    }

    public function delete(BatchDomain $batch) : bool {
        return Batch::find($batch->id)->delete();
    }
}
