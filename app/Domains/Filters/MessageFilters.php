<?php

namespace App\Domains\Filters;

class MessageFilters extends Filters
{
    public const ALLOWED_FILTERS = [
        'template_id',
        'campaign_id',
        'client_id',
        'sent_at_greater_than',
        'sent_at_lower_than',
        'scheduled_at_greater_than',
        'scheduled_at_lower_than',
        'is_read',
        'is_sent',
        'is_fail',
    ];

    public function __construct(array $requestData)
    {
        parent::__construct(self::ALLOWED_FILTERS, $requestData);
    }
}
