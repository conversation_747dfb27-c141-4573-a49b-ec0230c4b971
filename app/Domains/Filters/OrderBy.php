<?php

namespace App\Domains\Filters;

class OrderBy {

    public string $order;
    public string $by;
    public int $limit;

    public const ALLOWED_FIELDS = [
        'order',
        'by',
        'limit'
    ];

    public function __construct(array $fields){
        $this->order = $fields['order'] ?? 'id';
        $this->by = $fields['by'] ?? 'DESC';
        $this->limit = $fields['limit'] ?? 20;
        if($this->limit == -1) {
            $this->limit = PHP_INT_MAX;
        }
    }
}
