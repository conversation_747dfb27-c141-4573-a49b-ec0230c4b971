<?php

namespace App\Domains\Inventory;

use Carbon\Carbon;

class CustomProduct
{
    public ?int $id;
    public ?int $project_id;
    public ?int $budget_id;
    public ?int $quantity;
    public ?float $value;
    public ?string $description;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?Project $project;
    public ?Budget $budget;

    public function __construct(
        ?int $id,
        ?int $project_id,
        ?int $budget_id,
        ?int $quantity,
        ?float $value,
        ?string $description,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Project $project = null,
        ?Budget $budget = null
    ){
        $this->id = $id;
        $this->project_id = $project_id;
        $this->budget_id = $budget_id;
        $this->quantity = $quantity;
        $this->value = $value;
        $this->description = $description;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->project = $project;
        $this->budget = $budget;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "project_id" => $this->project_id,
            "budget_id" => $this->budget_id,
            "quantity" => $this->quantity,
            "value" => $this->value,
            "description" => $this->description,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "project" => ($this->project) ? $this->project->toArray() : null,
            "budget" => ($this->budget) ? $this->budget->toArray() : null,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "project_id" => $this->project_id,
            "budget_id" => $this->budget_id,
            "quantity" => $this->quantity,
            "value" => $this->value,
            "description" => $this->description,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "project_id" => $this->project_id,
            "budget_id" => $this->budget_id,
            "quantity" => $this->quantity,
            "value" => $this->value,
            "description" => $this->description,
        ];
    }
}
