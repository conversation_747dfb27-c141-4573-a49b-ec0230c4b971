<?php

namespace App\Domains\Inventory;

use Carbon\Carbon;

class Batch
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $shop_id;
    public ?int $product_id;
    public ?string $batch_number;
    public ?string $name;
    public ?string $description;
    public ?int $quantity;
    public ?Carbon $produced_at;
    public ?Carbon $expired_at;
    public ?Carbon $processed_at;
    public ?bool $is_processed_at_stock;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?Product $product;
    public ?Shop $shop;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $shop_id,
        ?int $product_id,
        ?string $batch_number,
        ?string $name,
        ?string $description,
        ?int $quantity,
        ?Carbon $produced_at,
        ?Carbon $expired_at,
        ?Carbon $processed_at,
        ?bool $is_processed_at_stock,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Product $product = null,
        ?Shop $shop = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->shop_id = $shop_id;
        $this->product_id = $product_id;
        $this->batch_number = $batch_number;
        $this->name = $name;
        $this->description = $description;
        $this->quantity = $quantity;
        $this->produced_at = $produced_at;
        $this->expired_at = $expired_at;
        $this->processed_at = $processed_at;
        $this->is_processed_at_stock = $is_processed_at_stock;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->product = $product;
        $this->shop = $shop;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "shop_id" => $this->shop_id,
            "product_id" => $this->product_id,
            "batch_number" => $this->batch_number,
            "name" => $this->name,
            "description" => $this->description,
            "quantity" => $this->quantity,
            "produced_at" => $this->produced_at?->format("d/m/Y"),
            "expired_at" => $this->expired_at?->format("d/m/Y"),
            "processed_at" => $this->processed_at?->format("d/m/Y"),
            "is_processed_at_stock" => $this->is_processed_at_stock,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "product" => ($this->product) ? $this->product->toArray() : null,
            "shop" => ($this->shop) ? $this->shop->toArray() : null
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "shop_id" => $this->shop_id,
            "product_id" => $this->product_id,
            "batch_number" => $this->batch_number,
            "name" => $this->name,
            "description" => $this->description,
            "quantity" => $this->quantity,
            "produced_at" => $this->produced_at,
            "expired_at" => $this->expired_at,
            "processed_at" => $this->processed_at,
            "is_processed_at_stock" => $this->is_processed_at_stock,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "batch_number" => $this->batch_number,
            "name" => $this->name,
            "description" => $this->description,
            "quantity" => $this->quantity,
            "produced_at" => $this->produced_at,
            "expired_at" => $this->expired_at,
            "processed_at" => $this->processed_at,
            "is_processed_at_stock" => $this->is_processed_at_stock,
        ];
    }

    public function processAtStock() : void {
        $this->processed_at = Carbon::now();
        $this->is_processed_at_stock = true;
    }
}
