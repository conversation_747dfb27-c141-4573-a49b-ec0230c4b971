<?php

namespace App\Domains\Inventory;

use Carbon\Carbon;

class Project
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $client_id;
    public ?int $budget_id;
    public string $name;
    public ?string $description;
    public ?float $value;
    public ?float $cost;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?Client $client;
    public ?Budget $budget;

    /** @var ProjectProduct[] $products */
    public ?array $products;

    /** @var CustomProduct[] $customProducts */
    public ?array $customProducts;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $client_id,
        ?int $budget_id,
        string $name,
        ?string $description,
        ?float $value,
        ?float $cost,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Client $client = null,
        ?Budget $budget = null,
        ?array $products = null,
        ?array $customProducts = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->client_id = $client_id;
        $this->budget_id = $budget_id;
        $this->name = $name;
        $this->description = $description;
        $this->value = $value;
        $this->cost = $cost;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->client = $client;
        $this->budget = $budget;
        $this->products = $products;
        $this->customProducts = $customProducts;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "client_id" => $this->client_id,
            "budget_id" => $this->budget_id,
            "name" => $this->name,
            "description" => $this->description,
            "value" => $this->value,
            "cost" => $this->cost,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "client" => ($this->client) ? $this->client->toArray() : null,
            "budget" => ($this->budget) ? $this->budget->toArray() : null,
            "products" => $this->products,
            "customProducts" => $this->customProducts,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "client_id" => $this->client_id,
            "budget_id" => $this->budget_id,
            "name" => $this->name,
            "description" => $this->description,
            "value" => $this->value,
            "cost" => $this->cost,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "client_id" => $this->client_id,
            "budget_id" => $this->budget_id,
            "name" => $this->name,
            "description" => $this->description,
            "value" => $this->value,
            "cost" => $this->cost,
        ];
    }
}
