<?php

namespace App\Domains\Inventory;

use App\Domains\User;
use Carbon\Carbon;

class DepartmentUser
{
    public ?int $id;
    public ?int $user_id;
    public ?int $department_id;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?User $user;
    public ?Department $department;

    public function __construct(
        ?int $id,
        ?int $user_id,
        ?int $department_id,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?User $user = null,
        ?Department $department = null
    ){
        $this->id = $id;
        $this->user_id = $user_id;
        $this->department_id = $department_id;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->user = $user;
        $this->department = $department;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "user_id" => $this->user_id,
            "department_id" => $this->department_id,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "user" => ($this->user) ? $this->user->toArray() : null,
            "department" => ($this->department) ? $this->department->toArray() : null,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "user_id" => $this->user_id,
            "department_id" => $this->department_id,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "user_id" => $this->user_id,
            "department_id" => $this->department_id,
        ];
    }
}
