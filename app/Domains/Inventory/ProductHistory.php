<?php

namespace App\Domains\Inventory;

use App\Domains\User;
use Carbon\Carbon;

class ProductHistory
{
    public ?int $id;
    public ?int $user_id;
    public ?int $product_id;
    public ?string $field;
    public ?string $alias;
    public ?string $old;
    public ?string $new;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?User $user;
    public ?Product $product;

    public function __construct(
        ?int $id,
        ?int $user_id,
        ?int $product_id,
        ?string $field,
        ?string $alias,
        ?string $old,
        ?string $new,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?User $user = null,
        ?Product $product = null
    ){
        $this->id = $id;
        $this->user_id = $user_id;
        $this->product_id = $product_id;
        $this->field = $field;
        $this->alias = $alias;
        $this->old = $old;
        $this->new = $new;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->user = $user;
        $this->product = $product;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "user_id" => $this->user_id,
            "product_id" => $this->product_id,
            "field" => $this->field,
            "alias" => $this->alias,
            "old" => $this->old,
            "new" => $this->new,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "user" => ($this->user) ? $this->user->toArray() : null,
            "product" => ($this->product) ? $this->product->toArray() : null,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "user_id" => $this->user_id,
            "product_id" => $this->product_id,
            "field" => $this->field,
            "alias" => $this->alias,
            "old" => $this->old,
            "new" => $this->new,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "user_id" => $this->user_id,
            "product_id" => $this->product_id,
            "field" => $this->field,
            "alias" => $this->alias,
            "old" => $this->old,
            "new" => $this->new,
        ];
    }
}
