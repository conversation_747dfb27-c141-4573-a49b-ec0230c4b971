<?php

namespace App\Domains\Inventory;

use Carbon\Carbon;

class GroupProduct
{
    public ?int $id;
    public ?int $group_id;
    public ?int $product_id;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?Group $group;
    public ?Product $product;

    public function __construct(
        ?int $id,
        ?int $group_id,
        ?int $product_id,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Group $group = null,
        ?Product $product = null
    ){
        $this->id = $id;
        $this->group_id = $group_id;
        $this->product_id = $product_id;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->group = $group;
        $this->product = $product;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "group_id" => $this->group_id,
            "product_id" => $this->product_id,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "group" => ($this->group) ? $this->group->toArray() : null,
            "product" => ($this->product) ? $this->product->toArray() : null,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "group_id" => $this->group_id,
            "product_id" => $this->product_id,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "group_id" => $this->group_id,
            "product_id" => $this->product_id,
        ];
    }
}
