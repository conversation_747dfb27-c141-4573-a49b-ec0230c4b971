<?php

namespace App\Domains\Inventory\Report;

class StockEntryReport
{
    public ?int $total_quantity;
    public ?float $total_value;

    /** @var StockEntryGroup[] $stock_entries_groups  */
    public ?array $stock_entries_groups;

    public function __construct(
        ?int $total_quantity,
        ?float $total_value,
        ?array $stock_entries_groups
    ){
        $this->total_quantity = $total_quantity;
        $this->total_value = $total_value;
        $this->stock_entries_groups = $stock_entries_groups;
    }

    public function toArray(): array
    {
        return [
            "total_quantity" => $this->total_quantity,
            "total_value" => $this->total_value,
            "stock_entries_groups" => $this->stock_entries_groups,
        ];
    }
}
