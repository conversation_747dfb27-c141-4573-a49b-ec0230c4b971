<?php

namespace App\Domains\Inventory\Report;

class StockExitReport
{
    public ?int $total_quantity;
    public ?float $total_value;

    /** @var StockExitGroup[] $stock_exits_groups  */
    public ?array $stock_exits_groups;

    public function __construct(
        ?int $total_quantity,
        ?float $total_value,
        ?array $stock_exits_groups
    ){
        $this->total_quantity = $total_quantity;
        $this->total_value = $total_value;
        $this->stock_exits_groups = $stock_exits_groups;
    }

    public function toArray(): array
    {
        return [
            "total_quantity" => $this->total_quantity,
            "total_value" => $this->total_value,
            "stock_exits_groups" => $this->stock_exits_groups,
        ];
    }
}
