<?php

namespace App\Domains\Inventory\Report;

use App\Domains\Filters\BatchFilters;
use App\Domains\Filters\BrandFilters;
use App\Domains\Filters\BudgetFilters;
use App\Domains\Filters\ClientFilters;
use App\Domains\Filters\GroupFilters;
use App\Domains\Filters\ProductFilters;
use App\Domains\Filters\ProjectFilters;
use App\Domains\Filters\StockEntryFilters;
use App\Domains\Filters\StockExitFilters;
use App\Domains\Filters\StockFilters;
use App\Domains\Filters\UserFilters;
use App\Repositories\BatchRepository;
use App\Repositories\BrandRepository;
use App\Repositories\BudgetRepository;
use App\Repositories\ClientRepository;
use App\Repositories\GroupRepository;
use App\Repositories\ProductRepository;
use App\Repositories\ProjectRepository;
use App\Repositories\StockEntryRepository;
use App\Repositories\StockExitRepository;
use App\Repositories\StockRepository;
use App\Repositories\UserRepository;

class CountSumReport
{
    public ?string $model;
    public ?string $column;
    public ?int $count = 0;
    public null|int|float $sum = 0;
    public mixed $repository = null;
    public mixed $filters = null;

    public const REPOSITORIES = [
        "products" => ProductRepository::class,
        "projects" => ProjectRepository::class,
        "clients" => ClientRepository::class,
        "budgets" => BudgetRepository::class,
        "stock_entries" => StockEntryRepository::class,
        "stock_exits" => StockExitRepository::class,
        "stocks" => StockRepository::class,
        "groups" => GroupRepository::class,
        "brands" => BrandRepository::class,
        "users" => UserRepository::class,
        "batches" => BatchRepository::class,
    ];

    public const FILTERS = [
        "products" => ProductFilters::class,
        "projects" => ProjectFilters::class,
        "clients" => ClientFilters::class,
        "budgets" => BudgetFilters::class,
        "stock_entries" => StockEntryFilters::class,
        "stock_exits" => StockExitFilters::class,
        "stocks" => StockFilters::class,
        "groups" => GroupFilters::class,
        "brands" => BrandFilters::class,
        "users" => UserFilters::class,
        "batches" => BatchFilters::class,
    ];

    public function __construct(string $model, string $column = null){
        $this->model = $model;
        $this->column = $column;
        $this->getNewRepository($model);
        $this->getNewFilters($model);
    }

    public function getNewRepository(string $model): void {
        if(!empty(self::REPOSITORIES[$model])) {
            $this->repository = app()->make(self::REPOSITORIES[$model]);
        }
    }
    public function getNewFilters(string $model): void {
        if(!empty(self::FILTERS[$model])) {
            $this->filters = app()->makeWith(self::FILTERS[$model], [
                "requestData" => request()->all()
            ]);
        }
    }

    public function getCount($organization_id): int {
        if(!$this->filters || !$this->repository){
            return $this->count;
        }

        $this->count = $this->repository->count(
            $organization_id,
            $this->filters
        );

        return $this->count;
    }

    public function getSum($organization_id): int|float {
        if(!$this->filters || !$this->repository || !$this->column){
            return $this->count;
        }

        $this->sum = $this->repository->sum(
            $organization_id,
            $this->filters,
            $this->column
        );

        return $this->sum;
    }

    public function toArray(): array {
        return [
            "count" => $this->count,
            "sum" => $this->sum,
            "model" => $this->model,
            "column" => $this->column
        ];
    }
}
