<?php

namespace App\UseCases\Log;

use App\Domains\Filters\LogFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\LogRepository;

class GetAll
{
    private LogRepository $logRepository;

    public function __construct(LogRepository $logRepository) {
        $this->logRepository = $logRepository;
    }

    /**
     * @param LogFilters $filters
     * @param OrderBy $orderBy
     * @return array
     */
    public function perform(LogFilters $filters, OrderBy $orderBy) : array {
        return $this->logRepository->fetchAll($filters, $orderBy);
    }
}
