<?php

namespace App\UseCases\ChatBot\Button;

use App\Domains\ChatBot\Component;
use App\Factories\ChatBot\ButtonFactory;
use App\Helpers\DBLog;
use App\Repositories\ButtonRepository;

class SaveFullButton
{
    private ButtonRepository $buttonRepository;
    private ButtonFactory $buttonFactory;

    public function __construct(ButtonRepository $buttonRepository, ButtonFactory $buttonFactory) {
        $this->buttonRepository = $buttonRepository;
        $this->buttonFactory = $buttonFactory;
    }

    /**
     * @param array $button
     * @param Component $componentDomain
     * @return void
     * @throws \Throwable
     */
    public function perform(array $button, Component $componentDomain) : void {
        try {
            $organization_id = request()->user()->organization_id ?? null;
            $user_id = request()->user()->id ?? null;
            $json = json_encode($button);
            $id = $button['id'] ?? null;

            $buttonDomain = $this->buttonFactory->buildFromSaveFullButton(
                $button,
                $json,
                $organization_id,
                $id
            );
            $this->buttonRepository->save($buttonDomain, $organization_id);
            $this->buttonRepository->attachComponent($buttonDomain, $componentDomain->id);
        } catch (\Throwable $exception) {
            DBLog::logError(
                $exception->getMessage() ?? null,
                "ChatBot::SaveFullButton",
                $organization_id ?? null,
                $user_id ?? null,
                $button
            );
            throw $exception;
        }
    }
}
