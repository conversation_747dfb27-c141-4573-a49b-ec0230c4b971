<?php

namespace App\UseCases\ChatBot\Button;

use App\Domains\Filters\ButtonFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\ButtonRepository;

class GetAll
{
    private ButtonRepository $buttonRepository;

    public function __construct(ButtonRepository $buttonRepository) {
        $this->buttonRepository = $buttonRepository;
    }

    /**
     * @return array
     */
    public function perform(ButtonFilters $filters, OrderBy $orderBy) : array {
        return $this->buttonRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
