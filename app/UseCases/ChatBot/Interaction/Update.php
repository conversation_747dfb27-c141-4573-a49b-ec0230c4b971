<?php

namespace App\UseCases\ChatBot\Interaction;

use App\Domains\ChatBot\Interaction;
use App\Factories\ChatBot\InteractionFactory;
use App\Http\Requests\Interaction\UpdateRequest;
use App\Repositories\InteractionRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private InteractionRepository $interactionRepository;
    private InteractionFactory $interactionFactory;

    public function __construct(InteractionRepository $interactionRepository, InteractionFactory $interactionFactory) {
        $this->interactionRepository = $interactionRepository;
        $this->interactionFactory = $interactionFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Interaction
     */
    public function perform(UpdateRequest $request, int $id) : Interaction {
        DB::beginTransaction();

        $domain = $this->interactionFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $interaction = $this->interactionRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $interaction;
    }
}
