<?php

namespace App\UseCases\ChatBot\Interaction;

use App\Domains\Filters\InteractionFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\InteractionRepository;

class GetAll
{
    private InteractionRepository $interactionRepository;

    public function __construct(InteractionRepository $interactionRepository) {
        $this->interactionRepository = $interactionRepository;
    }

    /**
     * @return array
     */
    public function perform(InteractionFilters $filters, OrderBy $orderBy) : array {
        return $this->interactionRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
