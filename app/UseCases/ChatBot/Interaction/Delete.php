<?php

namespace App\UseCases\ChatBot\Interaction;

use App\Repositories\InteractionRepository;
use Exception;

class Delete
{
    private InteractionRepository $interactionRepository;

    public function __construct(InteractionRepository $interactionRepository) {
        $this->interactionRepository = $interactionRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $interaction = $this->interactionRepository->fetchById($id);

        if($interaction->organization_id !== $organization_id){
            throw new Exception(
                "This interaction doesn't belong to this organization." ,
                403
            );
        }

        return $this->interactionRepository->delete($interaction);
    }
}
