<?php

namespace App\UseCases\ChatBot\Template;

use App\Domains\ChatBot\Template as TemplateDomain;
use App\Domains\ChatBot\TemplatePublishing as TemplatePublishingDomain;
use App\Factories\ChatBot\TemplatePublishingFactory;
use App\Helpers\DBLog;
use App\Repositories\TemplatePublishingRepository;
use App\Enums\PublishingService;
use Exception;
use Illuminate\Support\Facades\DB;


class PublishTemplate {
    private TemplatePublishingRepository $templatePublishingRepository;
    private TemplatePublishingFactory $templatePublishingFactory;

    public function __construct(
        TemplatePublishingRepository $templatePublishingRepository,
        TemplatePublishingFactory $templatePublishingFactory,
    ) {
        $this->templatePublishingRepository = $templatePublishingRepository;
        $this->templatePublishingFactory = $templatePublishingFactory;
    }

    /**
     * Publishes a Template Domain to the TemplatePublishing table.
     * Is already filtered by organization in the GetTemplate UseCase
     *
     * @param TemplateDomain $templateDomain
     * @param PublishingService $service
     * @return TemplatePublishingDomain
     * @throws Exception
     */
    public function perform(
        TemplateDomain $templateDomain,
        PublishingService $service
    ): TemplatePublishingDomain {
        DB::beginTransaction();

        $exist = $this->templatePublishingRepository->fetchByTemplateId($templateDomain->id);
        if($exist && $exist->id){
            if($exist->is_published){
                throw new Exception(
                    'Template already published: ' . $exist->published_at?->format('Y-m-d H:i:s')
                );
            }
            if($exist->is_queued){
                throw new Exception(
                    'Template is ready to be published and is in queue:'
                );
            }
            DBLog::logError(
                'Template is in publish but is not is_published nor is_queued. check what is going on',
                "PublishTemplate",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                $exist->toArray()
            );
        }

        $templatePublishingDomain = $this->templatePublishingFactory->buildFromTemplatePublish(
            $templateDomain->id, $service,
        );

        $savedDomain = $this->templatePublishingRepository->store(
            $templatePublishingDomain
        );

        DB::commit();

        return $savedDomain;
    }

}
