<?php

namespace App\UseCases\ChatBot\Template;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TemplateFilters;
use App\Repositories\TemplateRepository;

class GetAll
{
    private TemplateRepository $templateRepository;

    public function __construct(TemplateRepository $templateRepository) {
        $this->templateRepository = $templateRepository;
    }

    /**
     * @return array
     */
    public function perform(TemplateFilters $filters, OrderBy $orderBy) : array {
        return $this->templateRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
