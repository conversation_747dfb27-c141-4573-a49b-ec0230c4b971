<?php

namespace App\UseCases\ChatBot\Message;

use App\Domains\Filters\MessageFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\MessageRepository;

class GetAll
{
    private MessageRepository $messageRepository;

    public function __construct(MessageRepository $messageRepository) {
        $this->messageRepository = $messageRepository;
    }

    /**
     * @return array
     */
    public function perform(MessageFilters $filters, OrderBy $orderBy) : array {
        return $this->messageRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
