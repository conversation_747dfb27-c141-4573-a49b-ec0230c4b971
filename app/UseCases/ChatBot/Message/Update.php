<?php

namespace App\UseCases\ChatBot\Message;

use App\Domains\ChatBot\Message;
use App\Factories\ChatBot\MessageFactory;
use App\Http\Requests\Message\UpdateRequest;
use App\Repositories\MessageRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private MessageRepository $messageRepository;
    private MessageFactory $messageFactory;

    public function __construct(MessageRepository $messageRepository, MessageFactory $messageFactory) {
        $this->messageRepository = $messageRepository;
        $this->messageFactory = $messageFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Message
     */
    public function perform(UpdateRequest $request, int $id) : Message {
        DB::beginTransaction();

        $domain = $this->messageFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $message = $this->messageRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $message;
    }
}
