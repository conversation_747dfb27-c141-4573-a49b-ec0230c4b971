<?php

namespace App\UseCases\ChatBot\Message;

use App\Domains\ChatBot\Message;
use App\Enums\MessageStatus;
use App\Helpers\DBLog;
use App\Repositories\CampaignRepository;
use App\Repositories\MessageRepository;
use App\Services\Meta\WhatsApp\MessageService;
use Exception;

class Send
{
    private MessageRepository $messageRepository;

    public function __construct(MessageRepository $messageRepository) {
        $this->messageRepository = $messageRepository;
    }

    /**
     * @param Message $message
     * @param MessageService $service
     * @return void
     * @throws \Throwable
     */
    public function perform(Message $message, MessageService $service) : void {
        try{
            $service->send($message);

            $message->send();

            $this->messageRepository->update($message, $message->organization_id);
        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "ChatBot::Message::Send",
                $message->organization_id ?? null,
                null,
                $message->toArray()
            );

            $message->fail();

            $this->messageRepository->update($message, $message->organization_id);

            throw $e;
        }
    }
}
