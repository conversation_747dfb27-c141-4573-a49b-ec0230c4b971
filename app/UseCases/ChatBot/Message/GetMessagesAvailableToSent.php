<?php

namespace App\UseCases\ChatBot\Message;

use App\Repositories\MessageRepository;

class GetMessagesAvailableToSent
{
    private MessageRepository $messageRepository;

    public function __construct(MessageRepository $messageRepository) {
        $this->messageRepository = $messageRepository;
    }

    /**
     * Get messages that are ready to be sent
     *
     * @param int|null $limit
     * @return array
     */
    public function perform(?int $limit = 500): array
    {
        return $this->messageRepository->fetchAllAvailableToSend($limit);
    }
}
