<?php

namespace App\UseCases\ChatBot\Step;

use App\Repositories\StepRepository;
use Exception;

class Delete
{
    private StepRepository $stepRepository;

    public function __construct(StepRepository $stepRepository) {
        $this->stepRepository = $stepRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $step = $this->stepRepository->fetchById($id);

        if($step->organization_id !== $organization_id){
            throw new Exception(
                "This step don't belong to this organization." ,
                403
            );
        }

        return $this->stepRepository->delete($step);
    }
}
