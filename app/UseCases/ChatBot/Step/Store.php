<?php

namespace App\UseCases\ChatBot\Step;

use App\Domains\ChatBot\Step;
use App\Factories\ChatBot\StepFactory;
use App\Http\Requests\Step\StoreRequest;
use App\Repositories\StepRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private StepRepository $stepRepository;
    private StepFactory $stepFactory;

    public function __construct(StepRepository $stepRepository, StepFactory $stepFactory) {
        $this->stepRepository = $stepRepository;
        $this->stepFactory = $stepFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Step
     */
    public function perform(StoreRequest $request) : Step {
        DB::beginTransaction();

        $domain = $this->stepFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $step = $this->stepRepository->store($domain);

        DB::commit();

        return $step;
    }
}
