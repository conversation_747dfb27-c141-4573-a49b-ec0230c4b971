<?php

namespace App\UseCases\ChatBot\Step;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StepFilters;
use App\Repositories\StepRepository;

class GetAll
{
    private StepRepository $stepRepository;

    public function __construct(StepRepository $stepRepository) {
        $this->stepRepository = $stepRepository;
    }

    /**
     * @return array
     */
    public function perform(StepFilters $filters, OrderBy $orderBy) : array {
        return $this->stepRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
