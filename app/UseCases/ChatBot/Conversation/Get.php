<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\ChatBot\Conversation;
use App\Repositories\ConversationRepository;
use Exception;

class Get
{
    private ConversationRepository $conversationRepository;

    public function __construct(ConversationRepository $conversationRepository) {
        $this->conversationRepository = $conversationRepository;
    }

    /**
     * @param int $id
     * @return Conversation
     * @throws Exception
     */
    public function perform(int $id) : Conversation {
        $organization_id = request()->user()->organization_id;

        $conversation = $this->conversationRepository->fetchById($id);

        if($conversation->organization_id !== $organization_id){
            throw new Exception(
                "This conversation doesn't belong to this organization." ,
                403
            );
        }
        return $conversation;
    }
}
