<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\Filters\ConversationFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\ConversationRepository;

class GetAll
{
    private ConversationRepository $conversationRepository;

    public function __construct(ConversationRepository $conversationRepository) {
        $this->conversationRepository = $conversationRepository;
    }

    /**
     * @return array
     */
    public function perform(ConversationFilters $filters, OrderBy $orderBy) : array {
        return $this->conversationRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
