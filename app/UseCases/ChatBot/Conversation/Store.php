<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\ChatBot\Conversation;
use App\Factories\ChatBot\ConversationFactory;
use App\Http\Requests\Conversation\StoreRequest;
use App\Repositories\ConversationRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ConversationRepository $conversationRepository;
    private ConversationFactory $conversationFactory;

    public function __construct(ConversationRepository $conversationRepository, ConversationFactory $conversationFactory) {
        $this->conversationRepository = $conversationRepository;
        $this->conversationFactory = $conversationFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Conversation
     */
    public function perform(StoreRequest $request) : Conversation {
        DB::beginTransaction();

        $domain = $this->conversationFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $conversation = $this->conversationRepository->store($domain);

        DB::commit();

        return $conversation;
    }
}
