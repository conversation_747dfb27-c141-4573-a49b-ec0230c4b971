<?php

namespace App\UseCases\ChatBot\Flow;

use App\Domains\Filters\FlowFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\FlowRepository;

class GetAll
{
    private FlowRepository $flowRepository;

    public function __construct(FlowRepository $flowRepository) {
        $this->flowRepository = $flowRepository;
    }

    /**
     * @return array
     */
    public function perform(FlowFilters $filters, OrderBy $orderBy) : array {
        return $this->flowRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
