<?php

namespace App\UseCases\ChatBot\Flow;

use App\Domains\ChatBot\Flow;
use App\Factories\ChatBot\FlowFactory;
use App\Helpers\DBLog;
use App\Repositories\FlowRepository;
use App\UseCases\ChatBot\Step\SaveFullStep;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SaveFullFlow
{
    private FlowRepository $flowRepository;
    private FlowFactory $flowFactory;

    public function __construct(FlowRepository $flowRepository, FlowFactory $flowFactory) {
        $this->flowRepository = $flowRepository;
        $this->flowFactory = $flowFactory;
    }

    /**
     * @param Request $request
     * @return Flow
     */
    public function perform(Request $request) : Flow {
        DB::beginTransaction();

        try {
            $organization_id = request()->user()->organization_id ?? null;
            $user_id = request()->user()->id ?? null;

            $json = $request->only(['flow', 'steps']) ?? [];
            $flowArray = $request->flow ?? null;
            $steps = $request->steps ?? null;
            $steps_count = count($steps ?? []);
            $id = $request->flow['id'] ?? null;

            $flowDomain = $this->flowFactory->buildFromSaveFullFlow(
                $flowArray,
                json_encode($json),
                $steps_count,
                $organization_id ?? null,
                $id ?? null
            );

            $flow = $this->flowRepository->save($flowDomain, $organization_id);

            /** @var SaveFullStep $useCase */
            $useCase = app()->make(SaveFullStep::class);
            foreach ($steps as $index => $step) {
                $useCase->perform($step, (int) $index, $flow);
            }
        } catch (\Throwable $exception) {
            DB::rollBack();

            DBLog::logError(
                $exception->getMessage() ?? null,
                "ChatBot::SaveFullFlow",
                $organization_id ?? null,
                $user_id ?? null,
                $json ?? []
            );

            throw $exception;
        }
        DB::commit();
        return $flow;
    }
}
