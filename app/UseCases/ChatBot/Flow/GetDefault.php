<?php

namespace App\UseCases\ChatBot\Flow;

use App\Domains\ChatBot\Flow;
use App\Repositories\FlowRepository;

class GetDefault
{
    private FlowRepository $flowRepository;

    public function __construct(FlowRepository $flowRepository)
    {
        $this->flowRepository = $flowRepository;
    }

    public function perform(int $organization_id): ?Flow
    {
        return $this->flowRepository->fetchDefaultFlow($organization_id);
    }
}
