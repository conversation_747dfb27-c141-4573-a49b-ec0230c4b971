<?php

namespace App\UseCases\ChatBot\Flow;

use App\Domains\ChatBot\Flow;
use App\Repositories\FlowRepository;
use Exception;

class Get
{
    private FlowRepository $flowRepository;

    public function __construct(FlowRepository $flowRepository) {
        $this->flowRepository = $flowRepository;
    }

    /**
     * @param int $id
     * @return Flow
     * @throws Exception
     */
    public function perform(int $id) : Flow {
        $organization_id = request()->user()->organization_id;

        $flow = $this->flowRepository->fetchById($id);

        if($flow->organization_id !== $organization_id){
            throw new Exception(
                "This flow don't belong to this organization." ,
                403
            );
        }
        return $flow;
    }
}
