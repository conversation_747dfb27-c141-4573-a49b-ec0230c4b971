<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Domains\Filters\CampaignFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\CampaignRepository;

class GetAll
{
    private CampaignRepository $campaignRepository;

    public function __construct(CampaignRepository $campaignRepository) {
        $this->campaignRepository = $campaignRepository;
    }

    /**
     * @return array
     */
    public function perform(CampaignFilters $filters, OrderBy $orderBy) : array {
        return $this->campaignRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
