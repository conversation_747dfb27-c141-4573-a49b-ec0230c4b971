<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Factories\ChatBot\MessageFactory;
use App\Repositories\CampaignRepository;
use App\Repositories\MessageRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class GenerateMessages
{
    private CampaignRepository $campaignRepository;
    private MessageRepository $messageRepository;
    private MessageFactory $messageFactory;

    public function __construct(
        CampaignRepository $campaignRepository,
        MessageRepository $messageRepository,
        MessageFactory $messageFactory
    ) {
        $this->campaignRepository = $campaignRepository;
        $this->messageRepository = $messageRepository;
        $this->messageFactory = $messageFactory;
    }

    /**
     * Generate messages for a campaign
     *
     * @param int $campaign_id
     * @return array
     * @throws \Exception
     */
    public function perform(
        int $campaign_id
    ): array {
        DB::beginTransaction();

        $campaign = $this->campaignRepository->fetchFullById($campaign_id);
        $organization_id = request()->user()->organization_id;
        if ($organization_id !== $campaign->organization_id) {
            throw new \Exception("This campaign don't belong to this organization.", 403);
        }

        $messages = [];

        foreach ($campaign->clients ?? [] as $client) {
            $messageDomain = $this->messageFactory->buildFromGeneration(
                $campaign,
                $client,
                $organization_id
            );
            $messages[] = $this->messageRepository->store($messageDomain);
        }

        $campaign->message_count = count($messages);

        $campaign->is_sending = (!($campaign->is_scheduled) || Carbon::now()->gte($campaign->scheduled_at));

        $this->campaignRepository->update($campaign, $organization_id);

        DB::commit();

        return $messages;
    }
}
