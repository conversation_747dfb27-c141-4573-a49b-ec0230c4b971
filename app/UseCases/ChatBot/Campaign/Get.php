<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Domains\ChatBot\Campaign;
use App\Repositories\CampaignRepository;
use Exception;

class Get
{
    private CampaignRepository $campaignRepository;

    public function __construct(CampaignRepository $campaignRepository) {
        $this->campaignRepository = $campaignRepository;
    }

    /**
     * @param int $id
     * @return Campaign
     * @throws Exception
     */
    public function perform(int $id) : Campaign {
        $organization_id = request()->user()->organization_id;

        $campaign = $this->campaignRepository->fetchById($id);

        if($campaign->organization_id !== $organization_id){
            throw new Exception(
                "This campaign don't belong to this organization." ,
                403
            );
        }
        return $campaign;
    }
}
