<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Domains\Filters\CampaignFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\CampaignRepository;
use Exception;

class GetByPhoneNumber
{
    private CampaignRepository $campaignRepository;

    public function __construct(CampaignRepository $campaignRepository) {
        $this->campaignRepository = $campaignRepository;
    }

    /**
     * @param int $phoneNumberId
     * @param CampaignFilters $filters
     * @param OrderBy $orderBy
     * @return array
     * @throws Exception
     */
    public function perform(int $phoneNumberId, CampaignFilters $filters, OrderBy $orderBy) : array {
        return $this->campaignRepository->fetchFromPhoneNumber($phoneNumberId, $filters, $orderBy);
    }
}
