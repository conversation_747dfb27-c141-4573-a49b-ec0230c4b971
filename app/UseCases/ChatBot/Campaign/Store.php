<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Domains\ChatBot\Campaign;
use App\Factories\ChatBot\CampaignFactory;
use App\Http\Requests\Campaign\StoreRequest;
use App\Repositories\CampaignRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private CampaignRepository $campaignRepository;
    private CampaignFactory $campaignFactory;

    public function __construct(CampaignRepository $campaignRepository, CampaignFactory $campaignFactory) {
        $this->campaignRepository = $campaignRepository;
        $this->campaignFactory = $campaignFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Campaign
     */
    public function perform(StoreRequest $request) : Campaign {
        DB::beginTransaction();

        $domain = $this->campaignFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $campaign = $this->campaignRepository->store($domain);

        DB::commit();

        return $campaign;
    }
}
