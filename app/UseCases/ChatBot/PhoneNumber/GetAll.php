<?php

namespace App\UseCases\ChatBot\PhoneNumber;

use App\Domains\Filters\PhoneNumberFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\PhoneNumberRepository;

class GetAll
{
    private PhoneNumberRepository $phoneNumberRepository;

    public function __construct(PhoneNumberRepository $phoneNumberRepository) {
        $this->phoneNumberRepository = $phoneNumberRepository;
    }

    /**
     * @return array
     */
    public function perform(PhoneNumberFilters $filters, OrderBy $orderBy) : array {
        return $this->phoneNumberRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}