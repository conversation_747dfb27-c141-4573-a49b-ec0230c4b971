<?php

namespace App\UseCases\ChatBot\Component;

use App\Domains\ChatBot\Component;
use App\Repositories\ComponentRepository;
use Exception;

class Get
{
    private ComponentRepository $componentRepository;

    public function __construct(ComponentRepository $componentRepository) {
        $this->componentRepository = $componentRepository;
    }

    /**
     * @param int $id
     * @return Component
     * @throws Exception
     */
    public function perform(int $id) : Component {
        $organization_id = request()->user()->organization_id;

        $component = $this->componentRepository->fetchById($id);

        if($component->organization_id !== $organization_id){
            throw new Exception(
                "This component doesn't belong to this organization." ,
                403
            );
        }
        return $component;
    }
}
