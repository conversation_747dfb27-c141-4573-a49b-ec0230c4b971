<?php

namespace App\UseCases\ChatBot\Component;

use App\Domains\Filters\ComponentFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\ComponentRepository;

class GetAll
{
    private ComponentRepository $componentRepository;

    public function __construct(ComponentRepository $componentRepository) {
        $this->componentRepository = $componentRepository;
    }

    /**
     * @return array
     */
    public function perform(ComponentFilters $filters, OrderBy $orderBy) : array {
        return $this->componentRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
