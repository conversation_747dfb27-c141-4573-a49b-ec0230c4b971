<?php

namespace App\UseCases\ChatBot\Parameter;

use App\Domains\ChatBot\Component;
use App\Factories\ChatBot\ParameterFactory;
use App\Helpers\DBLog;
use App\Repositories\ParameterRepository;

class SaveFullParameter
{
    private ParameterRepository $parameterRepository;
    private ParameterFactory $parameterFactory;

    public function __construct(ParameterRepository $parameterRepository, ParameterFactory $parameterFactory) {
        $this->parameterRepository = $parameterRepository;
        $this->parameterFactory = $parameterFactory;
    }

    /**
     * @param array $parameter
     * @param Component $component
     * @return void
     */
    public function perform(array $parameter, Component $component) : void {
        try {
            $organization_id = request()->user()->organization_id ?? null;
            $user_id = request()->user()->id ?? null;
            $id = $parameter['id'] ?? null;

            $domain = $this->parameterFactory->buildFromSaveFullParameter(
                $parameter,
                $component,
                $id
            );

            $this->parameterRepository->store($domain);
        } catch (\Throwable $exception) {
            DBLog::logError(
                $exception->getMessage() ?? null,
                "ChatBot::SaveFullParameter",
                $organization_id ?? null,
                $user_id ?? null,
                $parameter
            );
            throw $exception;
        }
    }
}
