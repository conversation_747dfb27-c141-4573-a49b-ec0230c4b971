<?php

namespace App\UseCases\ChatBot\Parameter;

use App\Domains\ChatBot\Parameter;
use App\Factories\ChatBot\ParameterFactory;
use App\Http\Requests\Parameter\UpdateRequest;
use App\Repositories\ParameterRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private ParameterRepository $parameterRepository;
    private ParameterFactory $parameterFactory;

    public function __construct(ParameterRepository $parameterRepository, ParameterFactory $parameterFactory) {
        $this->parameterRepository = $parameterRepository;
        $this->parameterFactory = $parameterFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Parameter
     */
    public function perform(UpdateRequest $request, int $id) : Parameter {
        DB::beginTransaction();

        $domain = $this->parameterFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $parameter = $this->parameterRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $parameter;
    }
}
