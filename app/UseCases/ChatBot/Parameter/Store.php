<?php

namespace App\UseCases\ChatBot\Parameter;

use App\Domains\ChatBot\Parameter;
use App\Factories\ChatBot\ParameterFactory;
use App\Http\Requests\Parameter\StoreRequest;
use App\Repositories\ParameterRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ParameterRepository $parameterRepository;
    private ParameterFactory $parameterFactory;

    public function __construct(ParameterRepository $parameterRepository, ParameterFactory $parameterFactory) {
        $this->parameterRepository = $parameterRepository;
        $this->parameterFactory = $parameterFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Parameter
     */
    public function perform(StoreRequest $request) : Parameter {
        DB::beginTransaction();

        $domain = $this->parameterFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $parameter = $this->parameterRepository->store($domain);

        DB::commit();

        return $parameter;
    }
}
