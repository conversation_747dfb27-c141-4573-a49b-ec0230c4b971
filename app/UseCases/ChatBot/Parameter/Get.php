<?php

namespace App\UseCases\ChatBot\Parameter;

use App\Domains\ChatBot\Parameter;
use App\Repositories\ParameterRepository;
use Exception;

class Get
{
    private ParameterRepository $parameterRepository;

    public function __construct(ParameterRepository $parameterRepository) {
        $this->parameterRepository = $parameterRepository;
    }

    /**
     * @param int $id
     * @return Parameter
     * @throws Exception
     */
    public function perform(int $id) : Parameter {
        $organization_id = request()->user()->organization_id;

        $parameter = $this->parameterRepository->fetchById($id);

        if($parameter->organization_id !== $organization_id){
            throw new Exception(
                "This parameter doesn't belong to this organization." ,
                403
            );
        }
        return $parameter;
    }
}
