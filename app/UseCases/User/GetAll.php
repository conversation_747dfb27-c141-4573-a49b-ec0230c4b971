<?php

namespace App\UseCases\User;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\UserFilters;
use App\Domains\User;
use App\Repositories\UserRepository;

class GetAll
{
    private UserRepository $userRepository;

    public function __construct(UserRepository $userRepository) {
        $this->userRepository = $userRepository;
    }

    /**
     * @return array
     */
    public function perform(UserFilters $filters, OrderBy $orderBy) : array {
        return $this->userRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
