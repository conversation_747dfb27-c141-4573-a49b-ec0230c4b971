<?php

namespace App\UseCases\Import;

use App\Domains\Imports\Import;
use App\Repositories\ImportRepository;
use Exception;

class Get
{
    private ImportRepository $importRepository;

    public function __construct(ImportRepository $importRepository) {
        $this->importRepository = $importRepository;
    }

    /**
     * @param int $id
     * @return Import
     * @throws Exception
     */
    public function perform(int $id) : Import {
        $organization_id = request()->user()->organization_id;

        $import = $this->importRepository->fetchById($id);

        if($import->organization_id !== $organization_id){
            throw new Exception(
                "This import don't belong to this organization." ,
                403
            );
        }
        return $import;
    }
}
