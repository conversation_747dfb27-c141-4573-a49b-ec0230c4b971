<?php

namespace App\UseCases\Import;

use App\Domains\Imports\Import;
use App\Factories\ImportFactory;
use App\Helpers\File;
use App\Http\Requests\Import\StoreRequest;
use App\Repositories\ImportRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private const FILE_STRING = "file";

    private ImportRepository $importRepository;
    private ImportFactory $importFactory;

    public function __construct(ImportRepository $importRepository, ImportFactory $importFactory) {
        $this->importRepository = $importRepository;
        $this->importFactory = $importFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Import
     */
    public function perform(StoreRequest $request) : Import {
        DB::beginTransaction();

        $file = new File($request->file(self::FILE_STRING));

        $domain = $this->importFactory->buildFromFile($file, $request);

        $domain->organization_id = request()->user()->organization_id;
        $domain->user_id = request()->user()->id;

        $domain->upload();
        $domain->getHeader();
        $domain->getMap();

        $import = $this->importRepository->store($domain);

        DB::commit();

        return $import;
    }
}
