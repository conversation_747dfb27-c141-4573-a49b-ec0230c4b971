<?php

namespace App\UseCases\Import;

use App\Repositories\ImportRepository;
use Exception;

class Delete
{
    private ImportRepository $importRepository;

    public function __construct(ImportRepository $importRepository) {
        $this->importRepository = $importRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $import = $this->importRepository->fetchById($id);

        if($import->organization_id !== $organization_id){
            throw new Exception(
                "This import don't belong to this organization." ,
                403
            );
        }

        return $this->importRepository->delete($import);
    }
}
