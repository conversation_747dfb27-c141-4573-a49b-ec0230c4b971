<?php

namespace App\UseCases\Auth;

use App\Domains\Auth\Credentials;
use App\Domains\User;
use App\Factories\Auth\CredentialsFactory;
use App\Factories\UserFactory;
use App\Http\Requests\Auth\LoginRequest;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Auth;

class Login
{
    public CredentialsFactory $credentialsFactory;
    public UserFactory $userFactory;
    public UserRepository $userRepository;

    public function __construct(
        CredentialsFactory $credentialsFactory,
        UserFactory $userFactory,
        UserRepository $userRepository
    ){
        $this->credentialsFactory = $credentialsFactory;
        $this->userFactory = $userFactory;
        $this->userRepository = $userRepository;
    }

    public function perform(LoginRequest $request) : ?User {
        $credentials = $this->credentialsFactory->buildFromRequest($request);

        $isMultipleLogin = $this->isMultipleLogin($credentials);
        if($isMultipleLogin) {
            return $this->multipleLogin($request, $credentials);
        }

        if (Auth::attempt($credentials->toArray())) {
            $user = $this->userRepository->fetchByEmail($credentials->email);

            $user->token = $request->user()->createToken("token")->plainTextToken;

            return $user;
        }

        return null;
    }

    private function multipleLogin(LoginRequest $request, Credentials $credentials) : ?User
    {
        $emails = $this->userRepository->fetchAllByEmail($credentials->email);
        foreach ($emails as $email) {
            $newCredentials = $credentials->toArray();
            $newCredentials['organization_id'] = $email->organization_id;
            if (Auth::attempt($newCredentials)) {
                $user = $this->userRepository->fetchByEmail($credentials->email, $email->organization_id);

                $user->token = $request->user()->createToken("token")->plainTextToken;

                return $user;
            }
        }
        return null;
    }

    private function isMultipleLogin(Credentials $credentials): bool {
        return $this->userRepository->fetchByEmailCount($credentials->email) > 1;
    }
}
