<?php

namespace App\UseCases\Auth;

use App\Domains\User;
use App\Factories\UserFactory;
use App\Http\Requests\Auth\RegisterRequest;
use App\Repositories\UserRepository;
use App\UseCases\Organization\Store;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Register
{
    public UserFactory $userFactory;
    public UserRepository $userRepository;

    public function __construct(
        UserFactory $userFactory,
        UserRepository $userRepository
    ){
        $this->userFactory = $userFactory;
        $this->userRepository = $userRepository;
    }

    public function perform(RegisterRequest $request) : ?User {
        DB::beginTransaction();

        $userDomain = $this->userFactory->buildFromRegisterRequest($request);
        if($userDomain->organization){
            /** @var Store $registerUseCase */
            $organizationUseCase = app()->make(Store::class);
            $organizationUseCase->perform($userDomain->organization);
            $userDomain->organization_id = $userDomain->organization->id;
        }
        $user = $this->userRepository->store(
            $userDomain
        );

        Auth::loginUsingId($user->id);

        $user->token = $request->user()->createToken("token")->plainTextToken;

        DB::commit();

        return $user;
    }
}
