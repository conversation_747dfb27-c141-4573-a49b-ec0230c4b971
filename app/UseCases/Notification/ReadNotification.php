<?php

namespace App\UseCases\Notification;

use Exception;
use Illuminate\Http\Request;

class ReadNotification
{
    public function __construct() {
    }

    /**
     * @return Notification
     * @throws Exception
     */
    public function perform(int $id) {
        $notification = auth()->user()->unreadNotifications->where('id' , $id)->first();
        
        if(!$notification){
            return ['error' => 'Notification ID was not found.'];
        }
        
        $notification->markAsRead();
        
        return ['success' => 'Notification read with success.'];
    }
}
