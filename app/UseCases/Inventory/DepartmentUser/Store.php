<?php

namespace App\UseCases\Inventory\DepartmentUser;

use App\Domains\Inventory\DepartmentUser;
use App\Factories\Inventory\DepartmentUserFactory;
use App\Http\Requests\DepartmentUser\StoreRequest;
use App\Repositories\DepartmentUserRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private DepartmentUserRepository $departmentUserRepository;
    private DepartmentUserFactory $departmentUserFactory;

    public function __construct(DepartmentUserRepository $departmentUserRepository, DepartmentUserFactory $departmentUserFactory) {
        $this->departmentUserRepository = $departmentUserRepository;
        $this->departmentUserFactory = $departmentUserFactory;
    }

    /**
     * @param StoreRequest $request
     * @return DepartmentUser
     */
    public function perform(StoreRequest $request) : DepartmentUser {
        DB::beginTransaction();

        //Teoricamente não preciso do organization_id aq, certo? Pois no banco não uso pra essa tabela.
        $domain = $this->departmentUserFactory->buildFromStoreRequest($request);
        //$domain->organization_id = request()->user()->organization_id;

        $departmentUser = $this->departmentUserRepository->store($domain);

        DB::commit();

        return $departmentUser;
    }
}
