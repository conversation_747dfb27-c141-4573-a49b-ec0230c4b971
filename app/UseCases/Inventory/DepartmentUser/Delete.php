<?php

namespace App\UseCases\Inventory\DepartmentUser;

use App\Repositories\DepartmentUserRepository;
use Exception;

class Delete
{
    private DepartmentUserRepository $departmentUserRepository;

    public function __construct(DepartmentUserRepository $departmentUserRepository) {
        $this->departmentUserRepository = $departmentUserRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        return $this->departmentUserRepository->delete(
            $this->departmentUserRepository->fetchById($id)
        );
    }
}
