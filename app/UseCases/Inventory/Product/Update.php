<?php

namespace App\UseCases\Inventory\Product;

use App\Domains\Inventory\Product;
use App\Factories\Inventory\ProductFactory;
use App\Http\Requests\Product\UpdateRequest;
use App\Repositories\ProductRepository;
use App\UseCases\Inventory\ProductHistory\StoreFromProductUpdate;
use App\UseCases\Inventory\Stock\RefreshProductStockFromPriceChange;
use Illuminate\Support\Facades\DB;

class Update
{
    private ProductRepository $productRepository;
    private ProductFactory $productFactory;

    public function __construct(ProductRepository $productRepository, ProductFactory $productFactory) {
        $this->productRepository = $productRepository;
        $this->productFactory = $productFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Product
     */
    public function perform(UpdateRequest $request, int $id) : Product {
        DB::beginTransaction();
        $old_product = $this->productRepository->fetchById($id);

        $domain = $this->productFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        if($old_product->price !== $domain->price){
            $domain->updateLastPricedAt();
        }
        $product = $this->productRepository->update(
            $domain,
            request()->user()->organization_id
        );

        if($old_product->price !== $domain->price){
            /** @var StoreFromProductUpdate $historyUseCase */
            $historyUseCase = app()->make(StoreFromProductUpdate::class);
            $historyUseCase->perform($product, $old_product);

            /** @var RefreshProductStockFromPriceChange $useCaseRefreshProductStockFromPriceChange */
            $useCaseRefreshProductStockFromPriceChange = app()->make(RefreshProductStockFromPriceChange::class);
            $useCaseRefreshProductStockFromPriceChange->perform($domain);
        }
        DB::commit();

        return $product;
    }
}
