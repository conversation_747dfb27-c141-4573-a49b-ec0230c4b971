<?php

namespace App\UseCases\Inventory\Product;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\ProductFilters;
use App\Domains\Inventory\Product;
use App\Repositories\ProductRepository;

class GetAll
{
    private ProductRepository $productRepository;

    public function __construct(ProductRepository $productRepository) {
        $this->productRepository = $productRepository;
    }

    /**
     * @return array
     */
    public function perform(ProductFilters $filters, OrderBy $orderBy) : array {
        return $this->productRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
