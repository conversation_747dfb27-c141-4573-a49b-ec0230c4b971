<?php

namespace App\UseCases\Inventory\Product;

use App\Domains\Inventory\Product;
use App\Factories\Inventory\ProductFactory;
use App\Http\Requests\Product\StoreRequest;
use App\Repositories\ProductRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ProductRepository $productRepository;
    private ProductFactory $productFactory;

    public function __construct(ProductRepository $productRepository, ProductFactory $productFactory) {
        $this->productRepository = $productRepository;
        $this->productFactory = $productFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Product
     */
    public function perform(StoreRequest $request) : Product {
        DB::beginTransaction();

        $domain = $this->productFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $product = $this->productRepository->store($domain);

        DB::commit();

        return $product;
    }
}
