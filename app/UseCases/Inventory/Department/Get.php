<?php

namespace App\UseCases\Inventory\Department;

use App\Domains\Inventory\Department;
use App\Repositories\DepartmentRepository;
use Exception;

class Get
{
    private DepartmentRepository $departmentRepository;

    public function __construct(DepartmentRepository $departmentRepository) {
        $this->departmentRepository = $departmentRepository;
    }

    /**
     * @param int $id
     * @return Department
     * @throws Exception
     */
    public function perform(int $id) : Department {
        $organization_id = request()->user()->organization_id;

        $sale = $this->departmentRepository->fetchById($id);

        if($sale->organization_id !== $organization_id){
            throw new Exception(
                "This department don't belong to this organization." ,
                403
            );
        }
        return $sale;
    }
}
