<?php

namespace App\UseCases\Inventory\Department;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\DepartmentFilters;
use App\Domains\Inventory\Department;
use App\Repositories\DepartmentRepository;

class GetAll
{
    private DepartmentRepository $departmentRepository;

    public function __construct(DepartmentRepository $departmentRepository) {
        $this->departmentRepository = $departmentRepository;
    }

    /**
     * @return array
     */
    public function perform(DepartmentFilters $filters, OrderBy $orderBy) : array {
        return $this->departmentRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
