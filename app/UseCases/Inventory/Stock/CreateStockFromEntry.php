<?php

namespace App\UseCases\Inventory\Stock;

use App\Domains\Inventory\Stock;
use App\Domains\Inventory\StockEntry;
use App\Factories\Inventory\StockFactory;
use App\Repositories\StockRepository;

class CreateStockFromEntry
{
    private StockRepository $stockRepository;
    private StockFactory $stockFactory;

    public function __construct(
        StockRepository $stockRepository,
        StockFactory $stockFactory,
    ) {
        $this->stockRepository = $stockRepository;
        $this->stockFactory = $stockFactory;
    }

    /**
     * @param StockEntry $stockEntry
     * @return Stock
     */
    public function perform(StockEntry $stockEntry) : Stock {
        return $this->stockRepository->store(
            $this->stockFactory->buildFromStockEntry($stockEntry)
        );
    }
}
