<?php

namespace App\UseCases\Inventory\Stock;

use App\Domains\Inventory\Stock;
use App\Factories\Inventory\StockFactory;
use App\Http\Requests\Stock\UpdateRequest;
use App\Repositories\StockRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private StockRepository $stockRepository;
    private StockFactory $stockFactory;

    public function __construct(StockRepository $stockRepository, StockFactory $stockFactory) {
        $this->stockRepository = $stockRepository;
        $this->stockFactory = $stockFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Stock
     */
    public function perform(UpdateRequest $request, int $id) : Stock {
        DB::beginTransaction();
        $domain = $this->stockFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $stock = $this->stockRepository->update(
            $domain,
            request()->user()->organization_id
        );
        DB::commit();

        return $stock;
    }
}
