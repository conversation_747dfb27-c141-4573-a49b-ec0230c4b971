<?php

namespace App\UseCases\Inventory\Sale;

use App\Domains\Inventory\Sale;
use App\Factories\Inventory\SaleFactory;
use App\Http\Requests\Sale\StoreRequest;
use App\Repositories\SaleRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private SaleRepository $saleRepository;
    private SaleFactory $saleFactory;

    public function __construct(SaleRepository $saleRepository, SaleFactory $saleFactory) {
        $this->saleRepository = $saleRepository;
        $this->saleFactory = $saleFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Sale
     */
    public function perform(StoreRequest $request) : Sale {
        DB::beginTransaction();

        $domain = $this->saleFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $sale = $this->saleRepository->store($domain);

        DB::commit();

        return $sale;
    }
}
