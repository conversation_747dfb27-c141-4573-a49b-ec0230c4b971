<?php

namespace App\UseCases\Inventory\StockExit;

use App\Domains\Inventory\StockExit;
use App\Factories\Inventory\StockExitFactory;
use App\Http\Requests\StockExit\StoreRequest;
use App\Repositories\StockExitRepository;
use App\UseCases\Inventory\Stock\DecreaseStock;
use Exception;
use Illuminate\Support\Facades\DB;

class Store
{
    private StockExitRepository $stockExitRepository;
    private StockExitFactory $stockExitFactory;

    public function __construct(StockExitRepository $stockExitRepository, StockExitFactory $stockExitFactory) {
        $this->stockExitRepository = $stockExitRepository;
        $this->stockExitFactory = $stockExitFactory;
    }

    /**
     * @param StoreRequest $request
     * @return StockExit
     * @throws Exception
     */
    public function perform(StoreRequest $request) : StockExit {
        DB::beginTransaction();

        $domain = $this->stockExitFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;
        $domain->calculateValue();

        $stockExit = $this->stockExitRepository->store($domain);

        /** @var DecreaseStock $decreaseStockUseCase */
        $decreaseStockUseCase = app()->make(DecreaseStock::class);
        $decreaseStockUseCase->perform($stockExit);

        DB::commit();

        return $stockExit;
    }
}
