<?php

namespace App\UseCases\Inventory\StockExit;

use App\Domains\Inventory\StockExit;
use App\Factories\Inventory\StockExitFactory;
use App\Http\Requests\StockExit\UpdateRequest;
use App\Repositories\StockExitRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private StockExitRepository $stockExitRepository;
    private StockExitFactory $stockExitFactory;

    public function __construct(StockExitRepository $stockExitRepository, StockExitFactory $stockExitFactory) {
        $this->stockExitRepository = $stockExitRepository;
        $this->stockExitFactory = $stockExitFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return StockExit
     */
    public function perform(UpdateRequest $request, int $id) : StockExit {
        DB::beginTransaction();
        $domain = $this->stockExitFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $stockExit = $this->stockExitRepository->update(
            $domain,
            request()->user()->organization_id
        );
        DB::commit();

        return $stockExit;
    }
}
