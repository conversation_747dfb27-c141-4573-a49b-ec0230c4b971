<?php

namespace App\UseCases\Inventory\Budget;

use App\Domains\Inventory\Budget;
use App\Domains\Inventory\ProductsAttachs\AttachCustomDomain;
use App\Domains\Inventory\ProductsAttachs\AttachProductsDomain;
use App\Factories\Inventory\BudgetFactory;
use App\Repositories\BudgetRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AttachProducts
{
    private BudgetRepository $budgetRepository;
    private BudgetFactory $budgetFactory;

    public function __construct(BudgetRepository $budgetRepository, BudgetFactory $budgetFactory) {
        $this->budgetRepository = $budgetRepository;
        $this->budgetFactory = $budgetFactory;
    }

    /**
     * @param Request $request
     * @return Budget
     */
    public function perform(Request $request, Budget $budget) : Budget {
        DB::beginTransaction();

        $this->budgetRepository->clearProducts($budget);
        $this->budgetRepository->clearCustoms($budget);

        $products = new AttachProductsDomain($request->products ?? []);
        $customs = new AttachCustomDomain($request->customs ?? []);

        $this->budgetRepository->attachProducts($budget, $products);
        $this->budgetRepository->attachCustoms($budget, $customs);

        DB::commit();

        return $budget;
    }
}
