<?php

namespace App\UseCases\Inventory\Budget;

use App\Domains\Filters\BudgetFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\BudgetRepository;

class GetAll
{
    private BudgetRepository $budgetRepository;

    public function __construct(BudgetRepository $budgetRepository) {
        $this->budgetRepository = $budgetRepository;
    }

    /**
     * @return array
     */
    public function perform(BudgetFilters $filters, OrderBy $orderBy) : array {
        return $this->budgetRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
