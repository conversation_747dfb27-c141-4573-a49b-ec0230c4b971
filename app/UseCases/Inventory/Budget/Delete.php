<?php

namespace App\UseCases\Inventory\Budget;

use App\Repositories\BudgetRepository;
use Exception;

class Delete
{
    private BudgetRepository $budgetRepository;

    public function __construct(BudgetRepository $budgetRepository) {
        $this->budgetRepository = $budgetRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $budget = $this->budgetRepository->fetchById($id);

        if($budget->organization_id !== $organization_id){
            throw new Exception(
                "This budget don't belong to this organization." ,
                403
            );
        }

        return $this->budgetRepository->delete($budget);
    }
}
