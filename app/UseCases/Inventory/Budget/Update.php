<?php

namespace App\UseCases\Inventory\Budget;

use App\Domains\Inventory\Budget;
use App\Factories\Inventory\BudgetFactory;
use App\Http\Requests\Budget\UpdateRequest;
use App\Repositories\BudgetRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private BudgetRepository $budgetRepository;
    private BudgetFactory $budgetFactory;

    public function __construct(BudgetRepository $budgetRepository, BudgetFactory $budgetFactory) {
        $this->budgetRepository = $budgetRepository;
        $this->budgetFactory = $budgetFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Budget
     */
    public function perform(UpdateRequest $request, int $id) : Budget {
        DB::beginTransaction();
        $domain = $this->budgetFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $budget = $this->budgetRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $budget;
    }
}
