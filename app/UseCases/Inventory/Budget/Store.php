<?php

namespace App\UseCases\Inventory\Budget;

use App\Domains\Inventory\Budget;
use App\Factories\Inventory\BudgetFactory;
use App\Http\Requests\Budget\StoreRequest;
use App\Repositories\BudgetRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private BudgetRepository $budgetRepository;
    private BudgetFactory $budgetFactory;

    public function __construct(BudgetRepository $budgetRepository, BudgetFactory $budgetFactory) {
        $this->budgetRepository = $budgetRepository;
        $this->budgetFactory = $budgetFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Budget
     */
    public function perform(StoreRequest $request) : Budget {
        DB::beginTransaction();

        $domain = $this->budgetFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $budget = $this->budgetRepository->store($domain);

        DB::commit();

        return $budget;
    }
}
