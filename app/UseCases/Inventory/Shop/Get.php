<?php

namespace App\UseCases\Inventory\Shop;

use App\Domains\Inventory\Shop;
use App\Repositories\ShopRepository;
use Exception;

class Get
{
    private ShopRepository $shopRepository;

    public function __construct(ShopRepository $shopRepository) {
        $this->shopRepository = $shopRepository;
    }

    /**
     * @param int $id
     * @return Shop
     * @throws Exception
     */
    public function perform(int $id) : Shop {
        $organization_id = request()->user()->organization_id;

        $shop = $this->shopRepository->fetchById($id);

        if($shop->organization_id !== $organization_id){
            throw new Exception(
                "This shop don't belong to this organization." ,
                403
            );
        }
        return $shop;
    }
}
