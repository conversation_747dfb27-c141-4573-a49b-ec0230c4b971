<?php

namespace App\UseCases\Inventory\Batch;

use App\Repositories\BatchRepository;
use Exception;

class Delete
{
    private BatchRepository $batchRepository;

    public function __construct(BatchRepository $batchRepository) {
        $this->batchRepository = $batchRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $batch = $this->batchRepository->fetchById($id);

        if($batch->organization_id !== $organization_id){
            throw new Exception(
                "This batch don't belong to this organization." ,
                403
            );
        }

        return $this->batchRepository->delete($batch);
    }
}
