<?php

namespace App\UseCases\Inventory\ProjectProduct;

use App\Domains\Inventory\ProjectProduct;
use App\Repositories\ProjectProductRepository;

class GetAll
{
    private ProjectProductRepository $projectProductRepository;

    public function __construct(ProjectProductRepository $projectProductRepository) {
        $this->projectProductRepository = $projectProductRepository;
    }

    /**
     * @return array
     */
    public function perform() : array {
        return $this->projectProductRepository->fetchFromOrganization(
            request()->user()->organization_id
        );
    }
}
