<?php

namespace App\UseCases\Inventory\ProjectProduct;

use App\Domains\Inventory\ProjectProduct;
use App\Repositories\ProjectProductRepository;

class GetAllFromProject
{
    private ProjectProductRepository $projectProductRepository;

    public function __construct(ProjectProductRepository $projectProductRepository) {
        $this->projectProductRepository = $projectProductRepository;
    }

    /**
     * @return ProjectProduct[]
     */
    public function perform(int $project_id) : array {
        return $this->projectProductRepository->fetchFromProject(
            $project_id
        );
    }
}
