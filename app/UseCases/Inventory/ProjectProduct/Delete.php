<?php

namespace App\UseCases\Inventory\ProjectProduct;

use App\Repositories\ProjectProductRepository;
use Exception;

class Delete
{
    private ProjectProductRepository $projectProductRepository;

    public function __construct(ProjectProductRepository $projectProductRepository) {
        $this->projectProductRepository = $projectProductRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        return $this->projectProductRepository->delete(
            $this->projectProductRepository->fetchById($id)
        );
    }
}
