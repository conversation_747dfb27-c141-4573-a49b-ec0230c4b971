<?php

namespace App\UseCases\Inventory\Brand;

use App\Domains\Filters\BrandFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\BrandRepository;

class GetAll
{
    private BrandRepository $brandRepository;

    public function __construct(BrandRepository $brandRepository) {
        $this->brandRepository = $brandRepository;
    }

    /**
     * @return array
     */
    public function perform(BrandFilters $filters, OrderBy $orderBy) : array {
        return $this->brandRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
