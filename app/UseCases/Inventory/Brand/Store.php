<?php

namespace App\UseCases\Inventory\Brand;

use App\Domains\Inventory\Brand;
use App\Factories\Inventory\BrandFactory;
use App\Http\Requests\Brand\StoreRequest;
use App\Repositories\BrandRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private BrandRepository $brandRepository;
    private BrandFactory $brandFactory;

    public function __construct(BrandRepository $brandRepository, BrandFactory $brandFactory) {
        $this->brandRepository = $brandRepository;
        $this->brandFactory = $brandFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Brand
     */
    public function perform(StoreRequest $request) : Brand {
        DB::beginTransaction();

        $domain = $this->brandFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $brand = $this->brandRepository->store($domain);

        DB::commit();

        return $brand;
    }
}
