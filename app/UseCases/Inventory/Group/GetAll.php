<?php

namespace App\UseCases\Inventory\Group;

use App\Domains\Filters\GroupFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\GroupRepository;

class GetAll
{
    private GroupRepository $groupRepository;

    public function __construct(GroupRepository $groupRepository) {
        $this->groupRepository = $groupRepository;
    }

    /**
     * @return array
     */
    public function perform(GroupFilters $filters, OrderBy $orderBy) : array {
        return $this->groupRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
