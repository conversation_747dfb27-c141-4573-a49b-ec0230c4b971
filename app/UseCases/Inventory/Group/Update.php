<?php

namespace App\UseCases\Inventory\Group;

use App\Domains\Inventory\Group;
use App\Factories\Inventory\GroupFactory;
use App\Http\Requests\Group\UpdateRequest;
use App\Repositories\GroupRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private GroupRepository $groupRepository;
    private GroupFactory $groupFactory;

    public function __construct(GroupRepository $groupRepository, GroupFactory $groupFactory) {
        $this->groupRepository = $groupRepository;
        $this->groupFactory = $groupFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Group
     */
    public function perform(UpdateRequest $request, int $id) : Group {
        DB::beginTransaction();
        $domain = $this->groupFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $group = $this->groupRepository->update(
            $domain,
            request()->user()->organization_id
        );
        DB::commit();

        return $group;
    }
}
