<?php

namespace App\UseCases\Inventory\Group;

use App\Domains\Inventory\Group;
use App\Repositories\GroupRepository;
use Exception;

class Get
{
    private GroupRepository $groupRepository;

    public function __construct(GroupRepository $groupRepository) {
        $this->groupRepository = $groupRepository;
    }

    /**
     * @param int $id
     * @return Group
     * @throws Exception
     */
    public function perform(int $id) : Group {
        $organization_id = request()->user()->organization_id;

        $group = $this->groupRepository->fetchById($id);

        if($group->organization_id !== $organization_id){
            throw new Exception(
                "This group don't belong to this organization." ,
                403
            );
        }
        return $group;
    }
}
