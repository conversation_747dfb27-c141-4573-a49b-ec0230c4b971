<?php

namespace App\UseCases\Inventory\GroupProduct;

use App\Domains\Inventory\GroupProduct;
use App\Repositories\GroupProductRepository;

class GetAll
{
    private GroupProductRepository $groupProductRepository;

    public function __construct(GroupProductRepository $groupProductRepository) {
        $this->groupProductRepository = $groupProductRepository;
    }

    /**
     * @return array
     */
    public function perform() : array {
        return $this->groupProductRepository->fetchFromOrganization(
            request()->user()->organization_id
        );
    }
}
