<?php

namespace App\UseCases\Inventory\GroupProduct;

use App\Domains\Inventory\GroupProduct;
use App\Repositories\GroupProductRepository;
use Exception;

class Get
{
    private GroupProductRepository $groupProductRepository;

    public function __construct(GroupProductRepository $groupProductRepository) {
        $this->groupProductRepository = $groupProductRepository;
    }

    /**
     * @param int $id
     * @return GroupProduct
     * @throws Exception
     */
    public function perform(int $id) : GroupProduct {
        return $this->groupProductRepository->fetchById($id);
    }
}
