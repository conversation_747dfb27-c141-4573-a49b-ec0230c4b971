<?php

namespace App\UseCases\Inventory\GroupProduct;

use App\Domains\Inventory\GroupProduct;
use App\Factories\Inventory\GroupProductFactory;
use App\Http\Requests\GroupProduct\StoreRequest;
use App\Repositories\GroupProductRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private GroupProductRepository $groupProductRepository;
    private GroupProductFactory $groupProductFactory;

    public function __construct(GroupProductRepository $groupProductRepository, GroupProductFactory $groupProductFactory) {
        $this->groupProductRepository = $groupProductRepository;
        $this->groupProductFactory = $groupProductFactory;
    }

    /**
     * @param StoreRequest $request
     * @return GroupProduct
     */
    public function perform(StoreRequest $request) : GroupProduct {
        DB::beginTransaction();

        $domain = $this->groupProductFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $groupProduct = $this->groupProductRepository->store($domain);

        DB::commit();

        return $groupProduct;
    }
}
