<?php

namespace App\UseCases\Inventory\Report\Count;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockEntryReportFilters;
use App\Domains\Inventory\Report\CountSumReport;
use App\Domains\Inventory\Report\StockEntryReport;
use App\Repositories\StockEntryRepository;

class GetReport
{
    /**
     * @return CountSumReport
     */
    public function perform(string $model) : CountSumReport {
        $countReport = new CountSumReport($model);
        $countReport->getCount(
            request()->user()->organization_id,
        );

        return $countReport;
    }
}
