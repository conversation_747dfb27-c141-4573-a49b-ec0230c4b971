<?php

namespace App\UseCases\Inventory\BudgetProduct;

use App\Repositories\BudgetProductRepository;
use Exception;

class Delete
{
    private BudgetProductRepository $budgetProductRepository;

    public function __construct(BudgetProductRepository $budgetProductRepository) {
        $this->budgetProductRepository = $budgetProductRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        return $this->budgetProductRepository->delete(
            $this->budgetProductRepository->fetchById($id)
        );
    }
}
