<?php

namespace App\UseCases\Inventory\Project;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\ProjectFilters;
use App\Domains\Inventory\Project;
use App\Repositories\ProjectRepository;

class GetAll
{
    private ProjectRepository $projectRepository;

    public function __construct(ProjectRepository $projectRepository) {
        $this->projectRepository = $projectRepository;
    }

    /**
     * @return array
     */
    public function perform(ProjectFilters $filters, OrderBy $orderBy) : array {
        return $this->projectRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
