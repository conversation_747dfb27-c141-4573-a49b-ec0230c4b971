<?php

namespace App\UseCases\Inventory\Project;

use App\Domains\Inventory\Project;
use App\Factories\Inventory\ProjectFactory;
use App\Http\Requests\Project\StoreRequest;
use App\Repositories\ProjectRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ProjectRepository $projectRepository;
    private ProjectFactory $projectFactory;

    public function __construct(ProjectRepository $projectRepository, ProjectFactory $projectFactory) {
        $this->projectRepository = $projectRepository;
        $this->projectFactory = $projectFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Project
     */
    public function perform(StoreRequest $request) : Project {
        DB::beginTransaction();

        $domain = $this->projectFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $project = $this->projectRepository->store($domain);

        DB::commit();

        return $project;
    }
}
