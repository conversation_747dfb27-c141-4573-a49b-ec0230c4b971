<?php

namespace App\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Repositories\CustomProductRepository;
use Exception;

class Get
{
    private CustomProductRepository $customProductRepository;

    public function __construct(CustomProductRepository $customProductRepository) {
        $this->customProductRepository = $customProductRepository;
    }

    /**
     * @param int $id
     * @return CustomProduct
     * @throws Exception
     */
    public function perform(int $id) : CustomProduct {
        return $this->customProductRepository->fetchById($id);
    }
}
