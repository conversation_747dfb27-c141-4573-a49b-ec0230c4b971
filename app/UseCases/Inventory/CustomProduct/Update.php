<?php

namespace App\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Factories\Inventory\CustomProductFactory;
use App\Http\Requests\CustomProduct\UpdateRequest;
use App\Repositories\CustomProductRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private CustomProductRepository $customProductRepository;
    private CustomProductFactory $customProductFactory;

    public function __construct(CustomProductRepository $customProductRepository, CustomProductFactory $customProductFactory) {
        $this->customProductRepository = $customProductRepository;
        $this->customProductFactory = $customProductFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return CustomProduct
     */
    public function perform(UpdateRequest $request, int $id) : CustomProduct {
        DB::beginTransaction();
        $domain = $this->customProductFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $customProduct = $this->customProductRepository->update(
            $domain
        );

        DB::commit();

        return $customProduct;
    }
}
