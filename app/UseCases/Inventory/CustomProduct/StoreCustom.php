<?php

namespace App\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Factories\Inventory\CustomProductFactory;
use App\Http\Requests\CustomProduct\StoreRequest;
use App\Repositories\CustomProductRepository;

class StoreCustom
{
    private CustomProductRepository $projectProductRepository;
    private CustomProductFactory $projectProductFactory;

    public function __construct(CustomProductRepository $projectProductRepository, CustomProductFactory $projectProductFactory) {
        $this->projectProductRepository = $projectProductRepository;
        $this->projectProductFactory = $projectProductFactory;
    }

    /**
     * @param StoreRequest $request
     * @return CustomProduct
     */
    public function perform(StoreRequest $request) : CustomProduct {

        $domain = $this->projectProductFactory->buildFromStoreRequest($request);

        return $this->projectProductRepository->store($domain);
    }
}
