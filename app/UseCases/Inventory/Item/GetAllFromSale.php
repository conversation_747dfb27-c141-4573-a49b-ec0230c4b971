<?php

namespace App\UseCases\Inventory\Item;

use App\Domains\Inventory\Item;
use App\Repositories\ItemRepository;

class GetAllFromSale
{
    private ItemRepository $itemRepository;

    public function __construct(ItemRepository $itemRepository) {
        $this->itemRepository = $itemRepository;
    }

    /**
     * @return Item[]
     */
    public function perform(int $sale_id) : array {
        return $this->itemRepository->fetchFromSale(
            $sale_id
        );
    }
}
