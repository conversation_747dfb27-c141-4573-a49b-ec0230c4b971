<?php

namespace App\UseCases\Inventory\Item;

use App\Domains\Inventory\Item;
use App\Repositories\ItemRepository;
use Exception;

class Get
{
    private ItemRepository $itemRepository;

    public function __construct(ItemRepository $itemRepository) {
        $this->itemRepository = $itemRepository;
    }

    /**
     * @param int $id
     * @return Item
     * @throws Exception
     */
    public function perform(int $id) : Item {
        return $this->itemRepository->fetchById($id);
    }
}
