<?php

namespace App\UseCases\Inventory\Item;

use App\Repositories\ItemRepository;
use Exception;

class Delete
{
    private ItemRepository $itemRepository;

    public function __construct(ItemRepository $itemRepository) {
        $this->itemRepository = $itemRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        return $this->itemRepository->delete(
            $this->itemRepository->fetchById($id)
        );
    }
}
