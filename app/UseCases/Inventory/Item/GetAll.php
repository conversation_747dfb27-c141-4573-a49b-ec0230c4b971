<?php

namespace App\UseCases\Inventory\Item;

use App\Domains\Inventory\Item;
use App\Repositories\ItemRepository;

class GetAll
{
    private ItemRepository $itemRepository;

    public function __construct(ItemRepository $itemRepository) {
        $this->itemRepository = $itemRepository;
    }

    /**
     * @return array
     */
    public function perform() : array {
        return $this->itemRepository->fetchFromOrganization(
            request()->user()->organization_id
        );
    }
}
