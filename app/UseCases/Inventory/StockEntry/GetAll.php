<?php

namespace App\UseCases\Inventory\StockEntry;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockEntryFilters;
use App\Domains\Inventory\StockEntry;
use App\Repositories\StockEntryRepository;

class GetAll
{
    private StockEntryRepository $stockEntryRepository;

    public function __construct(StockEntryRepository $stockEntryRepository) {
        $this->stockEntryRepository = $stockEntryRepository;
    }

    /**
     * @return array
     */
    public function perform(StockEntryFilters $filters, OrderBy $orderBy) : array {
        return $this->stockEntryRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
