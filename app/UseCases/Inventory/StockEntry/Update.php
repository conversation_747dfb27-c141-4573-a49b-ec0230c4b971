<?php

namespace App\UseCases\Inventory\StockEntry;

use App\Domains\Inventory\StockEntry;
use App\Factories\Inventory\StockEntryFactory;
use App\Http\Requests\StockEntry\UpdateRequest;
use App\Repositories\StockEntryRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private StockEntryRepository $stockEntryRepository;
    private StockEntryFactory $stockEntryFactory;

    public function __construct(StockEntryRepository $stockEntryRepository, StockEntryFactory $stockEntryFactory) {
        $this->stockEntryRepository = $stockEntryRepository;
        $this->stockEntryFactory = $stockEntryFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return StockEntry
     */
    public function perform(UpdateRequest $request, int $id) : StockEntry {
        DB::beginTransaction();
        $domain = $this->stockEntryFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $stockEntry = $this->stockEntryRepository->update(
            $domain,
            request()->user()->organization_id
        );
        DB::commit();

        return $stockEntry;
    }
}
