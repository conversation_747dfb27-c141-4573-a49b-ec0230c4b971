<?php

namespace App\UseCases\Inventory\StockEntry;

use App\Domains\Inventory\StockEntry;
use App\Repositories\StockEntryRepository;
use Exception;

class Get
{
    private StockEntryRepository $stockEntryRepository;

    public function __construct(StockEntryRepository $stockEntryRepository) {
        $this->stockEntryRepository = $stockEntryRepository;
    }

    /**
     * @param int $id
     * @return StockEntry
     * @throws Exception
     */
    public function perform(int $id) : StockEntry {
        $organization_id = request()->user()->organization_id;

        $stockEntry = $this->stockEntryRepository->fetchById($id);

        if($stockEntry->organization_id !== $organization_id){
            throw new Exception(
                "This stockEntry don't belong to this organization." ,
                403
            );
        }
        return $stockEntry;
    }
}
