<?php

namespace App\UseCases\Inventory\Client;

use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\Client\UpdateRequest;
use App\Repositories\ClientRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private ClientRepository $clientRepository;
    private ClientFactory $clientFactory;

    public function __construct(ClientRepository $clientRepository, ClientFactory $clientFactory) {
        $this->clientRepository = $clientRepository;
        $this->clientFactory = $clientFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Client
     */
    public function perform(UpdateRequest $request, int $id) : Client {
        DB::beginTransaction();
        $domain = $this->clientFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $client = $this->clientRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $client;
    }
}
