<?php

namespace App\UseCases\Inventory\ProductHistory;

use App\Domains\Inventory\Product;
use App\Domains\Inventory\ProductHistory;
use App\Factories\Inventory\ProductHistoryFactory;
use App\Repositories\ProductHistoryRepository;

class StoreFromProductUpdate
{
    private ProductHistoryRepository $productHistoryRepository;
    private ProductHistoryFactory $productHistoryFactory;

    public function __construct(
        ProductHistoryRepository $productHistoryRepository,
        ProductHistoryFactory $productHistoryFactory
    ) {
        $this->productHistoryRepository = $productHistoryRepository;
        $this->productHistoryFactory = $productHistoryFactory;
    }

    /**
     * @param Product $product
     * @return ProductHistory
     */
    public function perform(Product $product, Product $old_product) : ProductHistory {
        return $this->productHistoryRepository->store(
            $this->productHistoryFactory->buildFromProducts($product, $old_product)
        );
    }
}
