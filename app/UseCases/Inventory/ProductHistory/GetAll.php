<?php

namespace App\UseCases\Inventory\ProductHistory;

use App\Domains\Inventory\ProductHistory;
use App\Repositories\ProductHistoryRepository;

class GetAll
{
    private ProductHistoryRepository $productHistoryRepository;

    public function __construct(ProductHistoryRepository $productHistoryRepository) {
        $this->productHistoryRepository = $productHistoryRepository;
    }

    /**
     * @return array
     */
    public function perform() : array {
        return $this->productHistoryRepository->fetchFromOrganization(
            request()->user()->organization_id
        );
    }
}
