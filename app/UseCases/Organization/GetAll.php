<?php

namespace App\UseCases\Organization;

use App\Domains\Organization;
use App\Repositories\OrganizationRepository;

class GetAll
{
    private OrganizationRepository $organizationRepository;

    public function __construct(OrganizationRepository $organizationRepository) {
        $this->organizationRepository = $organizationRepository;
    }

    /**
     * @return Organization[]
     */
    public function perform() : array {
        return $this->organizationRepository->fetchAll();
    }
}
