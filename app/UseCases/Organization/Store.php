<?php

namespace App\UseCases\Organization;

use App\Domains\Organization;
use App\Factories\OrganizationFactory;
use App\Http\Requests\Organization\StoreRequest;
use App\Repositories\OrganizationRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private OrganizationRepository $organizationRepository;
    private OrganizationFactory $organizationFactory;

    public function __construct(OrganizationRepository $organizationRepository, OrganizationFactory $organizationFactory) {
        $this->organizationRepository = $organizationRepository;
        $this->organizationFactory = $organizationFactory;
    }

    /**
     * @param Organization $organization
     * @return Organization
     */
    public function perform(Organization $organization) : Organization {
        DB::beginTransaction();

        $this->organizationRepository->store($organization);

        DB::commit();

        return $organization;
    }
}
