<?php

namespace App\UseCases\Profile;

use App\Domains\Profile;
use App\Repositories\ProfileRepository;
use Exception;

class Get
{
    private ProfileRepository $profileRepository;

    public function __construct(ProfileRepository $profileRepository) {
        $this->profileRepository = $profileRepository;
    }

    /**
     * @param int $id
     * @return Profile
     * @throws Exception
     */
    public function perform(int $id) : Profile {
        return $this->profileRepository->fetchById($id);
    }
}
