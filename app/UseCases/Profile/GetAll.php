<?php

namespace App\UseCases\Profile;

use App\Domains\Profile;
use App\Repositories\ProfileRepository;

class GetAll
{
    private ProfileRepository $profileRepository;

    public function __construct(ProfileRepository $profileRepository) {
        $this->profileRepository = $profileRepository;
    }

    /**
     * @return Profile[]
     */
    public function perform() : array {
        return $this->profileRepository->fetchAll();
    }
}
