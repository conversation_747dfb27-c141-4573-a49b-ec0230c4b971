<?php

namespace App\Services\Telegram\Factories;

use App\Factories\UserFactory;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\TelegramUser\StoreRequest;
use App\Http\Requests\TelegramUser\UpdateRequest;
use App\Services\Telegram\Domains\TelegramUser;
use App\Services\Telegram\Models\TelegramUser as TelegramUserModel;

class TelegramUserFactory
{
    public UserFactory $userFactory;
    public ClientFactory $clientFactory;
    public function __construct(UserFactory $userFactory, ClientFactory $clientFactory){
        $this->userFactory = $userFactory;
        $this->clientFactory = $clientFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : TelegramUser {
        return new TelegramUser(
            null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->telegram_id ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : TelegramUser {
        return new TelegramUser(
            null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->telegram_id ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(?TelegramUserModel $telegramUser) : ?TelegramUser {
        if(!$telegramUser){
            return null;
        }

        return new TelegramUser(
            $telegramUser->id ?? null,
            $telegramUser->user_id ?? null,
            $telegramUser->client_id ?? null,
            $telegramUser->telegram_id ?? null,
            $telegramUser->description ?? "",
            $this->userFactory->buildFromModel($telegramUser->user ?? null) ?? null,
            $this->clientFactory->buildFromModel($telegramUser->client ?? null) ?? null
        );
    }
}
