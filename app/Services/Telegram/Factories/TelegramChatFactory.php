<?php

namespace App\Services\Telegram\Factories;

use App\Factories\UserFactory;
use App\Factories\ChatBot\FlowFactory;
use App\Factories\ChatBot\StepFactory;
use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Factories\TelegramUserFactory;
use App\Services\Telegram\Telegram;
use App\Services\Telegram\Models\TelegramChat as TelegramChatModel;

class TelegramChatFactory
{
    public UserFactory $userFactory;
    public FlowFactory $flowFactory;
    public StepFactory $stepFactory;
    public TelegramUserFactory $telegramUserFactory;

    public function __construct(
        UserFactory $userFactory,
        FlowFactory $flowFactory,
        StepFactory $stepFactory,
        TelegramUserFactory $telegramUserFactory
    ){
        $this->userFactory = $userFactory;
        $this->flowFactory = $flowFactory;
        $this->stepFactory = $stepFactory;
        $this->telegramUserFactory = $telegramUserFactory;
    }

    public function buildFromUpdate(Telegram $telegram) : TelegramChat {
        return new TelegramChat(
            null,
            $telegram->telegramUser?->user->organization_id ?? null,
            $telegram->telegramUser?->user_id ?? null,
            $telegram->telegramUser?->id ?? null,
            $telegram->telegram_bot_id ?? null,
            $telegram->message->chat->id ?? null,
            $telegram->message->from->id ?? null,
            false,
            false,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            $telegram->telegramUser ?? null,
            null,
            null,
        );
    }

    public function buildFromModel(?TelegramChatModel $telegramChat) : ?TelegramChat {
        if(!$telegramChat){
            return null;
        }

        return new TelegramChat(
            $telegramChat->id ?? null,
            $telegramChat->organization_id ?? null,
            $telegramChat->user_id ?? null,
            $telegramChat->telegram_user_id ?? null,
            $telegramChat->bot_id ?? null,
            $telegramChat->chat_id ?? null,
            $telegramChat->from_id ?? null,
            $telegramChat->has_active_flow ?? null,
            $telegramChat->has_broken_flow ?? null,
            $telegramChat->current_flow ?? null,
            $telegramChat->current_flow_id ?? null,
            $telegramChat->current_flow_status ?? null,
            $telegramChat->current_step_id ?? null,
            $telegramChat->ocr_raw ?? null,
            $telegramChat->ocr_data ?? null,
            $telegramChat->created_at ?? null,
            $this->userFactory->buildFromModel($telegramChat->user ?? null) ?? null,
            $this->telegramUserFactory->buildFromModel($telegramChat->telegramUser ?? null) ?? null,
            $this->flowFactory->buildFromModel($telegramChat->flow ?? null) ?? null,
            $this->stepFactory->buildFromModel($telegramChat->step ?? null) ?? null,
        );
    }
}
