<?php

namespace App\Services\Telegram\Factories;

use App\Helpers\File;
use App\Services\Telegram\Domains\TelegramFile;
use App\Services\Telegram\Models\TelegramFile as TelegramFileModel;
use App\Services\Telegram\Telegram;

class TelegramFileFactory
{

    public function buildFromTelegram(Telegram $telegram, File $file): TelegramFile {
        return new TelegramFile(
            null,
            $telegram->organization->id ?? null,
            $telegram->user->id ?? null,
            $telegram->bot_id ?? null,
            $telegram->chat_id ?? null,
            $telegram->from->id ?? null,
            null,
            null,
            null,
            null,
            null,
            null,
            $file ?? null,
        );
    }

    public function buildFromModel(?TelegramFileModel $telegramFile) : ?TelegramFile {
        if (!$telegramFile){ return null; }

        return new TelegramFile(
            $telegramFile->id ?? null,
            $telegramFile->organization_id ?? null,
            $telegramFile->user_id ?? null,
            $telegramFile->bot_id ?? null,
            $telegramFile->chat_id ?? null,
            $telegramFile->from_id ?? null,
            $telegramFile->file ?? null,
            $telegramFile->filename ?? null,
            $telegramFile->filepath ?? null,
            $telegramFile->filesize ?? null,
            $telegramFile->file_extension ?? null,
            $telegramFile->file_mime_type ?? null,
            null,
            $telegramFile->created_at ?? null,
            $telegramFile->updated_at ?? null,
        );
    }
}
