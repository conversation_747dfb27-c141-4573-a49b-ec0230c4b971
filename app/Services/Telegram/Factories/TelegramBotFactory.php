<?php

namespace App\Services\Telegram\Factories;

use App\Enums\TelegramBotStatus;
use App\Enums\TelegramBotPublishingStatus;
use App\Factories\OrganizationFactory;
use App\Http\Requests\TelegramBot\StoreRequest;
use App\Http\Requests\TelegramBot\UpdateRequest;
use App\Services\Telegram\Domains\TelegramBot;
use App\Services\Telegram\Models\TelegramBot as TelegramBotModel;

class TelegramBotFactory
{
    private OrganizationFactory $organizationFactory;

    public function __construct(OrganizationFactory $organizationFactory)
    {
        $this->organizationFactory = $organizationFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : TelegramBot {
        return new TelegramBot(
            null,
            $request->organization_id ?? null,
            $request->telegram_id ?? null,
            $request->bot ?? null,
            $request->token ?? null,
            $request->url ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->is_active ?? null,
            $request->is_published ?? false,
            $request->status ? TelegramBotStatus::from($request->status) : TelegramBotStatus::is_draft,
            $request->publishing_status ? TelegramBotPublishingStatus::from($request->publishing_status) : TelegramBotPublishingStatus::is_draft,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : TelegramBot {
        return new TelegramBot(
            null,
            $request->organization_id ?? null,
            $request->telegram_id ?? null,
            $request->bot ?? null,
            $request->token ?? null,
            $request->url ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->is_active ?? null,
            $request->is_published ?? false,
            $request->status ? TelegramBotStatus::from($request->status) : TelegramBotStatus::is_draft,
            $request->publishing_status ? TelegramBotPublishingStatus::from($request->publishing_status) : TelegramBotPublishingStatus::is_draft,
        );
    }

    public function buildFromModel(?TelegramBotModel $telegramBot) : ?TelegramBot {
        if (!$telegramBot){ return null; }

        $organization = null;
        if ($telegramBot->organization_id) {
            $organization = $this->organizationFactory->buildFromModel($telegramBot->organization);
        }

        return new TelegramBot(
            $telegramBot->id ?? null,
            $telegramBot->organization_id ?? null,
            $telegramBot->telegram_id ?? null,
            $telegramBot->bot ?? null,
            $telegramBot->token ?? null,
            $telegramBot->url ?? null,
            $telegramBot->name ?? null,
            $telegramBot->description ?? null,
            $telegramBot->is_active ?? null,
            $telegramBot->is_published ?? null,
            $telegramBot->status ?? TelegramBotStatus::is_draft,
            $telegramBot->publishing_status ?? TelegramBotPublishingStatus::is_draft,
            $telegramBot->created_at ?? null,
            $telegramBot->updated_at ?? null,
            $organization
        );
    }
}
