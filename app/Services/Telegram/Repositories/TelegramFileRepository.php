<?php

namespace App\Services\Telegram\Repositories;

use App\Services\Telegram\Factories\TelegramFileFactory;
use App\Services\Telegram\Models\TelegramFile;
use App\Services\Telegram\Domains\TelegramFile as TelegramFileDomain;
use Illuminate\Support\Facades\Log;

class TelegramFileRepository

{
    private TelegramFileFactory $fileFactory;

    public function __construct(
        TelegramFileFactory $fileFactory,
    ){
        $this->fileFactory = $fileFactory;
    }


    public function store(TelegramFileDomain $file) : TelegramFileDomain {
        Log::info("[TelegramFileRepository::store]", [$file->chat_id, $file->toStoreArray()]);
        $savedTelegramFile = TelegramFile::create($file->toStoreArray());

        $file->id = $savedTelegramFile->id;

        return $file;
    }

    public function update(TelegramFileDomain $file) : TelegramFileDomain {
        TelegramFile::where('id', $file->id)
            ->update($file->toUpdateArray());

        return $file;
    }

    public function fetchById(int $id) : TelegramFileDomain {
        return $this->fileFactory->buildFromModel(
            TelegramFile::findOrFail($id)
        );
    }

    public function delete(TelegramFileDomain $file) : bool {
        return TelegramFile::find($file->id)->delete();
    }
}
