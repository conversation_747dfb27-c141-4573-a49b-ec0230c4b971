<?php

namespace App\Services\Telegram\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TelegramMessageFilters;
use App\Services\Telegram\Factories\TelegramMessageFactory;
use App\Services\Telegram\Models\TelegramMessage;
use App\Services\Telegram\Domains\TelegramMessage as TelegramMessageDomain;
use EloquentBuilder;

class TelegramMessageRepository

{
    private TelegramMessageFactory $chatFactory;

    public function __construct(
        TelegramMessageFactory $chatFactory,
    ){
        $this->chatFactory = $chatFactory;
    }

    /**
     * @param int $organization_id
     * @param TelegramMessageFilters $filters
     * @param OrderBy $orderBy
     * @return array
     */
    public function fetchFromOrganization(int $organization_id, TelegramMessageFilters $filters, OrderBy $orderBy) : array {
        $chats = [];

        $models = EloquentBuilder::to(TelegramMessage::class, $filters->filters)
            ->whereHas("chat" , function ($query) use ($organization_id) {
                $query->where("telegram_chats.organization_id", $organization_id);
            })->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $chats[] = $this->chatFactory->buildFromModel($model);
        }

        return [
            'data' => $chats,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function store(TelegramMessageDomain $chat) : TelegramMessageDomain {
        $savedTelegramMessage = TelegramMessage::create($chat->toStoreArray());

        $chat->id = $savedTelegramMessage->id;

        return $chat;
    }

    public function update(TelegramMessageDomain $chat) : TelegramMessageDomain {
        TelegramMessage::where('id', $chat->id)
            ->update($chat->toUpdateArray());

        return $chat;
    }

    public function fetchById(int $id) : TelegramMessageDomain {
        return $this->chatFactory->buildFromModel(
            TelegramMessage::findOrFail($id)
        );
    }

    public function fetchByMessageID(string $chat_id) : ?TelegramMessageDomain {
        return $this->chatFactory->buildFromModel(
            TelegramMessage::where("chat_id" , $chat_id)->first()
        );
    }
    public function delete(TelegramMessageDomain $chat) : bool {
        return TelegramMessage::find($chat->id)->delete();
    }
}
