<?php

namespace App\Services\Telegram\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TelegramChatFilters;
use App\Helpers\DBLog;
use App\Services\Telegram\Factories\TelegramChatFactory;
use App\Services\Telegram\Models\TelegramChat;
use App\Services\Telegram\Domains\TelegramChat as TelegramChatDomain;
use Illuminate\Support\Facades\Log;
use EloquentBuilder;

class TelegramChatRepository

{
    private TelegramChatFactory $chatFactory;

    public function __construct(
        TelegramChatFactory $chatFactory,
    ){
        $this->chatFactory = $chatFactory;
    }

    /**
     * @param int $organization_id
     * @param TelegramChatFilters $filters
     * @param OrderBy $orderBy
     * @return array
     */
    public function fetchFromOrganization(int $organization_id, TelegramChatFilters $filters, OrderBy $orderBy) : array {
        $chats = [];

        $models = EloquentBuilder::to(TelegramChat::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $chats[] = $this->chatFactory->buildFromModel($model);
        }

        return [
            'data' => $chats,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function store(TelegramChatDomain $chat) : TelegramChatDomain {
        DBLog::log("[TelegramChatRepository::store]", "telegram", $chat->organization_id, $chat->user_id, [$chat->chat_id, $chat->toStoreArray()]);
        Log::info("[TelegramChatRepository::store]", [$chat->chat_id, $chat->toStoreArray()]);
        $savedTelegramChat = TelegramChat::create($chat->toStoreArray());

        $chat->id = $savedTelegramChat->id;

        return $chat;
    }

    public function update(TelegramChatDomain $chat) : TelegramChatDomain {
        TelegramChat::where('id', $chat->id)
            ->update($chat->toUpdateArray());

        return $chat;
    }

    public function fetchById(int $id) : TelegramChatDomain {
        return $this->chatFactory->buildFromModel(
            TelegramChat::with('user')
                ->with('user.organization')
                ->with('telegramUser')
                ->findOrFail($id)
        );
    }

    public function fetchByChatID(string $chat_id) : ?TelegramChatDomain {
        return $this->chatFactory->buildFromModel(
            TelegramChat::with('user')
                ->with('user.organization')
                ->with('telegramUser')
                ->where("chat_id" , $chat_id)
                ->first()
        );
    }
    public function delete(TelegramChatDomain $chat) : bool {
        return TelegramChat::find($chat->id)->delete();
    }
}
