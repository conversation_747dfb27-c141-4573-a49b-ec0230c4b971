<?php

namespace App\Services\Telegram\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TelegramBotFilters;
use App\Enums\TelegramBotPublishingStatus;
use App\Services\Telegram\Domains\TelegramBot as TelegramBotDomain;
use App\Services\Telegram\Factories\TelegramBotFactory;
use App\Services\Telegram\Models\TelegramBot;
use EloquentBuilder;

class TelegramBotRepository
{
    private TelegramBotFactory $telegramBotFactory;

    public function __construct(TelegramBotFactory $telegramBotFactory){
        $this->telegramBotFactory = $telegramBotFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(TelegramBotFilters $filters, OrderBy $orderBy) : array {
        $telegramBots = [];

        $models = EloquentBuilder::to(TelegramBot::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $telegramBots[] = $this->telegramBotFactory->buildFromModel($model);
        }

        return [
            'data' => $telegramBots,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, TelegramBotFilters $filters, OrderBy $orderBy) : array {
        $telegramBots = [];

        $models = EloquentBuilder::to(TelegramBot::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $telegramBots[] = $this->telegramBotFactory->buildFromModel($model);
        }

        return [
            'data' => $telegramBots,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, TelegramBotFilters $filters): int {
        return EloquentBuilder::to(TelegramBot::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, TelegramBotFilters $filters, string $column): float|int {
        return EloquentBuilder::to(TelegramBot::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(TelegramBotDomain $telegramBot) : TelegramBotDomain {
        $savedTelegramBot = TelegramBot::create($telegramBot->toStoreArray());

        $telegramBot->id = $savedTelegramBot->id;

        return $telegramBot;
    }

    public function update(TelegramBotDomain $telegramBot, int $organization_id) : TelegramBotDomain {
        TelegramBot::where('id', $telegramBot->id)
            ->where('organization_id', $organization_id)
            ->update($telegramBot->toUpdateArray());

        return $telegramBot;
    }

    public function fetchById(int $id) : TelegramBotDomain {
        return $this->telegramBotFactory->buildFromModel(
            TelegramBot::findOrFail($id)
        );
    }

    public function fetchByBot(string $bot) : TelegramBotDomain {
        return $this->telegramBotFactory->buildFromModel(
            TelegramBot::where('bot', $bot)->first()
        );
    }

    public function delete(TelegramBotDomain $telegramBot) : bool {
        return TelegramBot::find($telegramBot->id)->delete();
    }

    public function fetchPendingPublicationBots() : array {
        $telegramBots = [];

        $models = TelegramBot::where('publishing_status', TelegramBotPublishingStatus::pending_publication->value)->get();

        foreach ($models as $model){
            $telegramBots[] = $this->telegramBotFactory->buildFromModel($model);
        }

        return $telegramBots;
    }
}
