<?php

namespace App\Services\Telegram\UseCases\Commands\Inventory;

use App\Services\Telegram\Domains\Messages\Inventory\BeginInventory;
use App\Services\Telegram\Telegram;

class InventoryMenu {

    public function __construct() {}

    /**
     * @param Telegram $telegram
     * @return void
     */
    public function perform(Telegram $telegram) : void {
        $telegram->log("[Command::Inventory::InventoryMenu::perform]", [
            "user_id" => $telegram->user?->id,
            "user_name" => $telegram->user?->first_name . " ". $telegram->user?->last_name
        ]);

        new BeginInventory($telegram);
    }
}
