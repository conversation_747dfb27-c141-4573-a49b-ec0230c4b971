<?php

namespace App\Services\Telegram\UseCases\Flows\ReadDocText;

use App\Helpers\DBLog;
use App\Services\Telegram\Telegram;
use Illuminate\Support\Facades\Log;

class MessageToDataArray
{

    public Telegram $telegram;

    public function __construct(Telegram $telegram){
        $this->telegram = $telegram;
    }

    public function perform(string $text): array {
        DBLog::log(
            "SendDocumentToWebhook::perform",
            "Telegram" ,
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            $text
        );

        $data = [];

        $data['client'] = $this->extractClient($text);

        return ["data" => $data, 'raw' => $text];
    }

    private function extractClient(string $text): string|null {
        try{
            return explode(" ",
                explode("Paciente:", $text)[1]
            )[0];
        } catch (\Throwable $e) {
            DBLog::logError(
            "MessageToDataArray::extractClient: ". $e->getMessage(),
            "Telegram" ,
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            );

            return null;
        }
    }

}
