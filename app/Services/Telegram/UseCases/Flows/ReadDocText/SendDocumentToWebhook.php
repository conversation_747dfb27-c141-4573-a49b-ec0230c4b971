<?php

namespace App\Services\Telegram\UseCases\Flows\ReadDocText;

use App\Helpers\DBLog;
use App\Services\Telegram\Telegram;
use Illuminate\Support\Facades\Http;

class SendDocumentToWebhook
{

    public Telegram $telegram;

    public const string WEBHOOK_URL = "https://api.billdoctor.com.br/api/webhook-create";
    public const string WEBHOOK_TOKEN = "7438923:jzhdsifhosdihfasdadsf8jxzcoa";

    public function __construct(Telegram $telegram){
        $this->telegram = $telegram;
    }

    public function perform(array $data): void {
        DBLog::log(
            "SendDocumentToWebhook::perform",
            "Telegram" ,
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            $data
        );

        try {
            $data['_token'] = self::WEBHOOK_TOKEN;

            Http::post(self::WEBHOOK_URL, $data);
        } catch (\Exception $e) {
            DBLog::logError(
                "SendDocumentToWebhook::perform - Failed to send webhook",
                "Telegram",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                [
                    'error' => $e->getMessage(),
                    'data' => $data
                ] ?? null
            );
        }
    }

}
