<?php

namespace App\Services\Telegram\UseCases\Flows\ReadDocText;

use App\Services\Telegram\Telegram;
use App\Services\Telegram\Traits\Flows\ReadDocText\WaitingForApproval;
use App\Services\Telegram\Traits\Flows\ReadDocText\WaitingForUpload;
use App\Services\Tesseract\Tesseract;
use Illuminate\Support\Facades\Log;

class RunCurrentStatus
{
    use WaitingForUpload;
    use WaitingForApproval;

    public const array FLOW_STATUSES = [
        "waiting_for_upload",
        "waiting_for_approval"
    ];

    public Telegram $telegram;
    public Tesseract $tesseract;
    public string $currentFlowStatus;

    public ?string $text;

    public function __construct(Telegram $telegram, Tesseract $tesseract){
        $this->telegram = $telegram;
        $this->currentFlowStatus = $this->telegram->currentFlowStatus;
        $this->tesseract = $tesseract;
    }

    public function run(): void {
        Log::info("ReadDocStatus::RunCurrentStatus");
        if (!in_array($this->currentFlowStatus, self::FLOW_STATUSES)) {
            Log::info("ReadDocStatus::RunCurrentStatus::invalidStatus", [$this->currentFlowStatus]);
            return;
        }
        switch ($this->currentFlowStatus) {
            case "waiting_for_upload":
                $this->waitingForUpload();
                break;
            case "waiting_for_approval":
                $this->waitingForApproval();
                break;
        }
    }

}
