<?php

namespace App\Services\Telegram\UseCases\TelegramMessage;

use App\Services\Telegram\Domains\TelegramMessage;
use App\Services\Telegram\Factories\TelegramMessageFactory;
use App\Http\Requests\TelegramMessage\StoreRequest;
use App\Services\Telegram\Repositories\TelegramMessageRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private TelegramMessageRepository $telegramMessageRepository;
    private TelegramMessageFactory $telegramMessageFactory;

    public function __construct(TelegramMessageRepository $telegramMessageRepository, TelegramMessageFactory $telegramMessageFactory) {
        $this->telegramMessageRepository = $telegramMessageRepository;
        $this->telegramMessageFactory = $telegramMessageFactory;
    }

    /**
     * @param StoreRequest $request
     * @return TelegramMessage
     */
    public function perform(StoreRequest $request) : TelegramMessage {
        DB::beginTransaction();

        $domain = $this->telegramMessageFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $telegramMessage = $this->telegramMessageRepository->store($domain);

        DB::commit();

        return $telegramMessage;
    }
}
