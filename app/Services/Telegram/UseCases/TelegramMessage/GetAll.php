<?php

namespace App\Services\Telegram\UseCases\TelegramMessage;

use App\Domains\Filters\TelegramMessageFilters;
use App\Domains\Filters\OrderBy;
use App\Services\Telegram\Repositories\TelegramMessageRepository;

class GetAll
{
    private TelegramMessageRepository $telegramMessageRepository;

    public function __construct(TelegramMessageRepository $telegramMessageRepository) {
        $this->telegramMessageRepository = $telegramMessageRepository;
    }

    /**
     * @return array
     */
    public function perform(TelegramMessageFilters $filters, OrderBy $orderBy) : array {
        return $this->telegramMessageRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
