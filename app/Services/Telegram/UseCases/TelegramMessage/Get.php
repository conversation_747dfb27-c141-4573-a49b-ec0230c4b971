<?php

namespace App\Services\Telegram\UseCases\TelegramMessage;

use App\Services\Telegram\Domains\TelegramMessage;
use App\Services\Telegram\Repositories\TelegramMessageRepository;
use Exception;

class Get
{
    private TelegramMessageRepository $telegramMessageRepository;

    public function __construct(TelegramMessageRepository $telegramMessageRepository) {
        $this->telegramMessageRepository = $telegramMessageRepository;
    }

    /**
     * @param int $id
     * @return TelegramMessage
     * @throws Exception
     */
    public function perform(int $id) : TelegramMessage {
        $organization_id = request()->user()->organization_id;

        $telegramMessage = $this->telegramMessageRepository->fetchById($id);

        if($telegramMessage->organization_id !== $organization_id){
            throw new Exception(
                "This telegramMessage don't belong to this organization." ,
                403
            );
        }
        return $telegramMessage;
    }
}
