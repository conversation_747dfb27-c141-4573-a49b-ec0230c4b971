<?php

namespace App\Services\Telegram\UseCases\CustomFlow;

use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Telegram;
use App\Services\Telegram\UseCases\Flows\FinishFlow;
use App\Services\Telegram\UseCases\UpdateChat;

class GoToNextStep
{
    public Telegram $telegram;
    public TelegramChat $telegramChat;
    public ?Flow $currentFlow;
    public ?Step $currentStep;
    public ?int $nextStep;

    public function __construct(Telegram $telegram)
    {
        $this->telegram = $telegram;
        $this->telegramChat = $telegram->telegramChat;
        $this->currentFlow = $telegram->customCurrentFlow;
        $this->currentStep = $telegram->currentStep;
        $this->nextStep = $telegram->currentStep->next_step;
    }

    /**
     * Updates the TelegramChat to the next step
     */
    public function perform(): void
    {
        if (!$this->currentStep || $this->currentStep->is_ending_step || !$this->nextStep) {
            if($this->currentStep->is_ending_step){
                /** @var FinishFlow $finishUseCase */
                $finishUseCase = app()->makeWith(FinishFlow::class, ["telegram" => $this->telegram]);
                $finishUseCase->perform(false);
            }
            return;
        }
        $nextStep = $this->currentFlow->getStepByPosition($this->nextStep);

        $this->telegramChat->current_step_id = $nextStep->id;

        /** @var UpdateChat $updateChat */
        $updateChat = app()->make(UpdateChat::class);
        $updateChat->perform($this->telegramChat);
    }
}
