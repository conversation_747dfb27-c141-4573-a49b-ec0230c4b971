<?php

namespace App\Services\Telegram\UseCases\CustomFlow;

use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Button;
use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use App\Helpers\DBLog;
use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Telegram;
use Illuminate\Contracts\Container\BindingResolutionException;
use Telegram\Bot\Exceptions\TelegramSDKException;

class Run
{
    public Telegram $telegram;
    public TelegramChat $chat;
    public Flow $flow;
    public Step $step;
    public Component $component;
    /** @var Button[]  */
    public array $buttons;

    public bool $hasButtons;

    public bool $isInitialStep;
    public bool $isFinalStep;
    public bool $isCommand;
    public bool $isMessage;
    public bool $isInteractive;
    public bool $isInput;

    public function __construct(Telegram $telegram)  {
        $this->telegram = $telegram;
        $this->chat = $telegram->telegramChat;
        $this->flow = $telegram->customCurrentFlow;
        $this->step = $telegram->currentStep;
        $this->component = $telegram->currentStep->component;
        $this->buttons = $telegram->currentStep->component->buttons ?? [];

        $this->hasButtons = (count($this->buttons) > 0);

        $this->isInitialStep = $this->step->is_initial_step;
        $this->isFinalStep = $this->step->is_ending_step;
        $this->isCommand = $this->step->is_command;
        $this->isMessage = $this->step->is_message;
        $this->isInteractive = $this->step->is_interactive;
        $this->isInput = $this->step->is_input;
    }

    /**
     * @throws BindingResolutionException
     * @throws TelegramSDKException
     */
    public function perform() : bool {
        try{
            if($this->isMessage){
                /** @var SendCustomMessage $useCase */
                $useCase = app()->makeWith(SendCustomMessage::class, ["telegram" => $this->telegram]);
                $useCase->perform($this->step);
            }

            if($this->isInput){
                /** @var ReceiveInput $useCase */
                $useCase = app()->makeWith(ReceiveInput::class, ["telegram" => $this->telegram]);
                $useCase->perform($this->step);
            }

            /** @var GoToNextStep $useCaseGo */
            $useCaseGo = app()->makeWith(GoToNextStep::class, ["telegram" => $this->telegram]);
            $useCaseGo->perform();

            return true;
        }catch (\Throwable $e){
            DBLog::logError(
                $e->getMessage(),
                "Domain::CustomFlow::Run",
                $this->telegram->organization->id ?? null,
                $this->telegram->user->id ?? null,
                ["client_id" => $this->telegram->client?->id] ?? null
            );
            return false;
        }
    }
}
