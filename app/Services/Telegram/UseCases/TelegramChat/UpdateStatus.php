<?php

namespace App\Services\Telegram\UseCases\TelegramChat;

use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Repositories\TelegramChatRepository;
use Illuminate\Support\Facades\DB;

class UpdateStatus
{
    private TelegramChatRepository $telegramChatRepository;

    public function __construct(TelegramChatRepository $telegramChatRepository) {
        $this->telegramChatRepository = $telegramChatRepository;
    }

    /**
     * @param TelegramChat $chat
     * @param string $status
     * @return TelegramChat
     */
    public function perform(TelegramChat $chat, string $status, string $raw) : TelegramChat {
        DB::beginTransaction();

        $chat->current_flow_status = $status;
        $chat->ocr_raw = $raw;
        $chat->ocr_data = $raw;
        $telegramChat = $this->telegramChatRepository->update(
            $chat
        );

        DB::commit();

        return $telegramChat;
    }
}
