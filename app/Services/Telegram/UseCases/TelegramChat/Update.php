<?php

namespace App\Services\Telegram\UseCases\TelegramChat;

use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Factories\TelegramChatFactory;
use App\Http\Requests\TelegramChat\UpdateRequest;
use App\Services\Telegram\Repositories\TelegramChatRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private TelegramChatRepository $telegramChatRepository;
    private TelegramChatFactory $telegramChatFactory;

    public function __construct(TelegramChatRepository $telegramChatRepository, TelegramChatFactory $telegramChatFactory) {
        $this->telegramChatRepository = $telegramChatRepository;
        $this->telegramChatFactory = $telegramChatFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return TelegramChat
     */
    public function perform(UpdateRequest $request, int $id) : TelegramChat {
        DB::beginTransaction();

        $domain = $this->telegramChatFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $telegramChat = $this->telegramChatRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $telegramChat;
    }
}
