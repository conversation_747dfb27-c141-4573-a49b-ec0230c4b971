<?php

namespace App\Services\Telegram\UseCases\TelegramChat;

use App\Domains\Filters\TelegramChatFilters;
use App\Domains\Filters\OrderBy;
use App\Services\Telegram\Repositories\TelegramChatRepository;

class GetAll
{
    private TelegramChatRepository $telegramChatRepository;

    public function __construct(TelegramChatRepository $telegramChatRepository) {
        $this->telegramChatRepository = $telegramChatRepository;
    }

    /**
     * @param TelegramChatFilters $filters
     * @param OrderBy $orderBy
     * @return array
     */
    public function perform(TelegramChatFilters $filters, OrderBy $orderBy) : array {
        return $this->telegramChatRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
