<?php

namespace App\Services\Telegram\UseCases\TelegramFile;

use App\Helpers\File;
use App\Services\Telegram\Domains\TelegramFile;
use App\Services\Telegram\Factories\TelegramFileFactory;
use App\Http\Requests\TelegramFile\StoreRequest;
use App\Services\Telegram\Helpers\TelegramFileManager;
use App\Services\Telegram\Repositories\TelegramFileRepository;
use App\Services\Telegram\Telegram;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Telegram\Bot\Exceptions\TelegramSDKException;
use Throwable;

class Upload
{
    private TelegramFileRepository $telegramFileRepository;
    private TelegramFileFactory $telegramFileFactory;

    public function __construct(TelegramFileRepository $telegramFileRepository, TelegramFileFactory $telegramFileFactory) {
        $this->telegramFileRepository = $telegramFileRepository;
        $this->telegramFileFactory = $telegramFileFactory;
    }

    /**
     * @param Telegram $service
     *
     * @return null|TelegramFile
     * @throws TelegramSDKException
     * @throws Throwable
     */
    public function perform(Telegram $service) : ?TelegramFile {
        try {
            $file = new File(TelegramFileManager::getTelegramUploadedFile($service));

            DB::beginTransaction();

            $domain = $this->telegramFileFactory->buildFromTelegram($service, $file);

            $domain->upload();

            $telegramFile = $this->telegramFileRepository->store($domain);

            DB::commit();

            return $telegramFile;
        } catch (Throwable $e) {
            DB::rollBack();

            Log::error("File upload error: " . $e->getMessage());

            $service->sendMessage("Erro ao processar o arquivo.");

            throw $e;
        }
    }
}
