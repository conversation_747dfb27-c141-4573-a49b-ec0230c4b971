<?php

namespace App\Services\Telegram\UseCases\TelegramUser;

use App\Services\Telegram\Domains\TelegramUser;
use App\Services\Telegram\Repositories\TelegramUserRepository;
use Exception;

class Get
{
    private TelegramUserRepository $telegramUserRepository;

    public function __construct(TelegramUserRepository $telegramUserRepository) {
        $this->telegramUserRepository = $telegramUserRepository;
    }

    /**
     * @param int $id
     * @return TelegramUser
     * @throws Exception
     */
    public function perform(int $id) : TelegramUser {
        return $this->telegramUserRepository->fetchById($id);
    }
}
