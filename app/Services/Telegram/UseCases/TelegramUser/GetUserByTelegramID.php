<?php

namespace App\Services\Telegram\UseCases\TelegramUser;

use App\Services\Telegram\Domains\TelegramUser;
use App\Services\Telegram\Repositories\TelegramUserRepository;
use Exception;

class GetUserByTelegramID
{
    private TelegramUserRepository $telegramUserRepository;

    public function __construct(TelegramUserRepository $telegramUserRepository) {
        $this->telegramUserRepository = $telegramUserRepository;
    }

    /**
     * @param int $telegram_id
     * @return null|TelegramUser
     * @throws Exception
     */
    public function perform(int $telegram_id) : ?TelegramUser {
        return $this->telegramUserRepository->fetchByTelegramId($telegram_id);
    }
}
