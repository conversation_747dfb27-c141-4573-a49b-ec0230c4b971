<?php

namespace App\Services\Telegram\UseCases\TelegramUser;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TelegramUserFilters;
use App\Services\Telegram\Repositories\TelegramUserRepository;

class GetAll
{
    private TelegramUserRepository $telegramUserRepository;

    public function __construct(TelegramUserRepository $telegramUserRepository) {
        $this->telegramUserRepository = $telegramUserRepository;
    }

    /**
     * @return array
     */
    public function perform(TelegramUserFilters $filters, OrderBy $orderBy) : array {
        return $this->telegramUserRepository->fetchFromUser(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
