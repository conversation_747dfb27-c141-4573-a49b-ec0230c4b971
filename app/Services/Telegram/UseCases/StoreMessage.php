<?php

namespace App\Services\Telegram\UseCases;

use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Domains\TelegramMessage;
use App\Services\Telegram\Factories\TelegramMessageFactory;
use App\Services\Telegram\Repositories\TelegramMessageRepository;
use Telegram\Bot\Objects\Update;

class StoreMessage
{
    public TelegramMessageRepository $messageRepository;
    public TelegramMessageFactory $messageFactory;

    public function __construct(
        TelegramMessageRepository $messageRepository,
        TelegramMessageFactory $messageFactory
    ) {
        $this->messageRepository = $messageRepository;
        $this->messageFactory = $messageFactory;
    }

    /**
     * @param Update $update
     * @param TelegramChat|null $chat
     * @return TelegramMessage
     */
    public function perform(Update $update, ?TelegramChat $chat = null) : TelegramMessage {
        return $this->messageRepository->store(
            $this->messageFactory->buildFromUpdate($update, $chat)
        );
    }
}
