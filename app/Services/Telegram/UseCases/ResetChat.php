<?php

namespace App\Services\Telegram\UseCases;

use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Repositories\TelegramChatRepository;

class ResetChat
{
    public TelegramChatRepository $telegramChatRepository;

    public function __construct(
        TelegramChatRepository $telegramChatRepository
    ) {
        $this->telegramChatRepository = $telegramChatRepository;
    }

    /**
     * @param TelegramChat $telegramChat
     * @return void
     */
    public function perform(TelegramChat $telegramChat) : void {

        $telegramChat->has_broken_flow = false;
        $telegramChat->has_active_flow = false;
        $telegramChat->current_flow = null;
        $telegramChat->current_flow_status = null;

        $this->telegramChatRepository->update($telegramChat);
    }
}
