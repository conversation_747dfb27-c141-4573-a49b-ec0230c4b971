<?php

namespace App\Services\Telegram\UseCases;

use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Repositories\TelegramChatRepository;

class UpdateChat
{
    private TelegramChatRepository $telegramChatRepository;

    public function __construct(TelegramChatRepository $telegramChatRepository)
    {
        $this->telegramChatRepository = $telegramChatRepository;
    }

    public function perform(TelegramChat $telegramChat): TelegramChat
    {
        return $this->telegramChatRepository->update($telegramChat);
    }
}
