<?php

namespace App\Services\Telegram\UseCases\TelegramBot;

use App\Services\Telegram\Domains\TelegramBot;
use App\Services\Telegram\Repositories\TelegramBotRepository;
use Exception;

class GetFromBotID
{
    private TelegramBotRepository $telegramBotRepository;

    public function __construct(TelegramBotRepository $telegramBotRepository) {
        $this->telegramBotRepository = $telegramBotRepository;
    }

    /**
     * @param string $bot
     * @return TelegramBot
     * @throws Exception
     */
    public function perform(string $bot) : TelegramBot {
        return $this->telegramBotRepository->fetchByBot($bot);
    }
}
