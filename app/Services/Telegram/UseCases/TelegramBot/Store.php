<?php

namespace App\Services\Telegram\UseCases\TelegramBot;

use App\Services\Telegram\Domains\TelegramBot;
use App\Services\Telegram\Factories\TelegramBotFactory;
use App\Http\Requests\TelegramBot\StoreRequest;
use App\Services\Telegram\Repositories\TelegramBotRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private TelegramBotRepository $telegramBotRepository;
    private TelegramBotFactory $telegramBotFactory;

    public function __construct(TelegramBotRepository $telegramBotRepository, TelegramBotFactory $telegramBotFactory) {
        $this->telegramBotRepository = $telegramBotRepository;
        $this->telegramBotFactory = $telegramBotFactory;
    }

    /**
     * @param StoreRequest $request
     * @return TelegramBot
     */
    public function perform(StoreRequest $request) : TelegramBot {
        DB::beginTransaction();

        $domain = $this->telegramBotFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $telegramBot = $this->telegramBotRepository->store($domain);

        DB::commit();

        return $telegramBot;
    }
}
