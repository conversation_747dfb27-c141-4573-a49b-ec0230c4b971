<?php

namespace App\Services\Telegram\UseCases\TelegramBot;

use App\Enums\TelegramBotPublishingStatus;
use App\Helpers\DBLog;
use App\Services\Telegram\Domains\TelegramBot;
use App\Services\Telegram\Repositories\TelegramBotRepository;
use App\Services\Telegram\Telegram;
use Telegram\Bot\Exceptions\TelegramSDKException;

class PublishPendingBots
{
    private TelegramBotRepository $telegramBotRepository;

    public function __construct(TelegramBotRepository $telegramBotRepository) {
        $this->telegramBotRepository = $telegramBotRepository;
    }

    /**
     * Publishes all pending bots by setting their webhooks
     *
     * @return array Array of published bot IDs and any errors
     * @throws \Exception
     */
    public function perform(): array {
        $pendingBots = $this->telegramBotRepository->fetchPendingPublicationBots();
        $results = [
            'published' => [],
            'errors' => []
        ];

        foreach ($pendingBots as $bot) {
            try {
                // Create Telegram service instance with the bot
                $telegramService = new Telegram($bot);

                // Set webhook with custom URL
                $webhookUrl = $telegramService->setWebhook(true);

                // Update bot status to published
                $bot->publishing_status = TelegramBotPublishingStatus::is_published;
                $this->telegramBotRepository->update($bot, $bot->organization_id);

                // Log successful webhook setting
                DBLog::log(
                    "Telegram bot webhook set successfully",
                    "telegram",
                    $bot->organization_id,
                    null,
                    [
                        'bot_id' => $bot->id,
                        'bot_name' => $bot->name,
                        'webhook_url' => $webhookUrl
                    ]
                );

                $results['published'][] = [
                    'id' => $bot->id,
                    'name' => $bot->name,
                    'webhook_url' => $webhookUrl
                ];
            } catch (TelegramSDKException $e) {
                // Update bot status to failed
                $bot->publishing_status = TelegramBotPublishingStatus::failed_publication;
                $this->telegramBotRepository->update($bot, $bot->organization_id);

                // Log error in webhook setting
                DBLog::logError(
                    "Failed to set webhook for Telegram bot",
                    "telegram",
                    $bot->organization_id,
                    null,
                    [
                        'bot_id' => $bot->id,
                        'bot_name' => $bot->name,
                        'error' => $e->getMessage()
                    ]
                );

                $results['errors'][] = [
                    'id' => $bot->id,
                    'name' => $bot->name,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }
}
