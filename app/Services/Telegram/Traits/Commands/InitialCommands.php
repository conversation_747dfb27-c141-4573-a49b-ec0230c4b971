<?php

namespace App\Services\Telegram\Traits\Commands;

use App\Services\Telegram\Domains\Messages\Begin;
use App\Services\Telegram\Domains\Messages\Canceled;
use App\Services\Telegram\UseCases\Commands\Commercial\CommercialMenu;
use App\Services\Telegram\UseCases\Commands\Inventory\InventoryMenu;
use App\Services\Telegram\UseCases\ResetChat;

trait InitialCommands
{
    protected const array TELEGRAM_COMMANDS = [
        "/quem-sou-eu",
        "/menu_inicial",
        "/back_to_begin",
        "/reset_chat",
        "/inventory",
        "/commercial",
        "/image_reader",
        "/cancel"
    ];

    protected function runInitialCommands() : void {
        switch ($this->command) {
            case "/menu_inicial":
            case "/back_to_begin":
                new Begin($this->service);
                break;
            case "/cancel":
                if(!empty($this->service->telegramSale)){
                    /** @var ResetChat $useCase */
                    $useCase = app()->make(ResetChat::class);
                    $useCase->perform($this->service->telegramChat);
                }

                new Canceled($this->service);
                new Begin($this->service);
                break;
            case "/reset_chat":
                if(!empty($this->service->telegramSale)){
                    /** @var ResetChat $useCase */
                    $useCase = app()->make(ResetChat::class);
                    $useCase->perform($this->service->telegramChat);
                }
                new Begin($this->service);
                break;
            case "/inventory":
                /** @var InventoryMenu $useCase */
                $useCase = app()->make(InventoryMenu::class);
                $useCase->perform($this->service);
                break;
            case "/commercial":
                /** @var CommercialMenu $useCase */
                $useCase = app()->make(CommercialMenu::class);
                $useCase->perform($this->service);
                break;
            case "/image_reader":
                break;
        }
    }
}
