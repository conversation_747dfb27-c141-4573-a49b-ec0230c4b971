<?php

namespace App\Services\Telegram\Traits\Commands;

use App\Services\Telegram\UseCases\Commands\DocumentReader\DocReaderStartFlow;

trait ReadDocCommands
{
    protected const array TELEGRAM_DOC_READ_COMMANDS = [
        "/image_reader"
    ];

    protected function runReadDocCommands() : void {
        switch ($this->command) {
            case "/image_reader":
                /** @var DocReaderStartFlow $useCase */
                $useCase = app()->make(DocReaderStartFlow::class);
                $useCase->perform($this->service);
                break;
        }
    }
}
