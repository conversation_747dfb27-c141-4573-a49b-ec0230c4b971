<?php

namespace App\Services\Telegram\Traits\Flows\ReadDocText;

use App\Services\Telegram\Domains\Messages\DocumentReader\IsDataCorrect;
use App\Services\Telegram\UseCases\Flows\FinishFlow;
use App\Services\Telegram\UseCases\TelegramChat\UpdateStatus;
use Illuminate\Support\Facades\Log;

trait WaitingForUpload
{
    private const string NEXT_FLOW_STATUS = "waiting_for_approval";

    public function waitingForUpload()
    {
        Log::info("WaitingForUpload::waitingForUpload");
        if (!$this->telegram->telegramFile) {
            $this->telegram->sendMessage("Ops! Não encontramos o seu arquivo, houve um erro no fluxo, tente novamente");
            return null;
        }

        $this->tesseract->setLang("por");
        $this->tesseract->load($this->telegram->telegramFile->file);
        $this->text = $this->tesseract->text;

        if($this->text && $this->text != ""){
            $this->telegram->sendMessage("Abaixo está o texto que conseguimos ler: ");
            $this->telegram->sendMessage($this->text);
            new IsDataCorrect($this->telegram);

            /** @var UpdateStatus $useCase */
            $useCase = app()->make(UpdateStatus::class);
            $useCase->perform($this->telegram->telegramChat, self::NEXT_FLOW_STATUS, $this->text);
        } else {
            $this->telegram->sendMessage("Ops! Não conseguimos ler o texto na imagem! ");

            /** @var FinishFlow $useCase */
            $useCase = app()->makeWith(FinishFlow::class, ["telegram" => $this->telegram]);
            $useCase->perform();
        }
    }
}
