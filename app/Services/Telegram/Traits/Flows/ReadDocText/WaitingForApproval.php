<?php

namespace App\Services\Telegram\Traits\Flows\ReadDocText;

use App\Helpers\DBLog;
use App\Services\Telegram\UseCases\Flows\FinishFlow;
use App\Services\Telegram\UseCases\Flows\ReadDocText\MessageToDataArray;
use App\Services\Telegram\UseCases\Flows\ReadDocText\SendDocumentToWebhook;
use Illuminate\Support\Facades\Log;

trait WaitingForApproval
{
    private const array IN_APPROVAL_ARRAY = ["sim", "s", "y", "yes"];

    public function waitingForApproval() : void
    {
        Log::info("WaitingForApproval::waitingForApproval");
        $response = $this->telegram->callbackData;
        if(!$response) {
            $response = $this->telegram->text;
        }

        DBLog::log(
            "WaitingForApproval::waitingForApproval",
            "Telegram",
            $this->telegram->telegramChat->organization_id,
            $this->telegram->telegramChat->user_id,
            ["callback_data" => $response]
        );

        if( in_array(strtolower($response), self::IN_APPROVAL_ARRAY)){
            /** @var MessageToDataArray $useCase */
            $useCase = app()->makeWith(MessageToDataArray::class, ["telegram" => $this->telegram]);
            $data = $useCase->perform($this->telegram->telegramChat->ocr_data);

            /** @var SendDocumentToWebhook $useCaseSend */
            $useCaseSend = app()->makeWith(SendDocumentToWebhook::class, ["telegram" => $this->telegram]);
            $useCaseSend->perform($data);

            $this->telegram->sendMessage("Dados enviados com sucesso!");

            /** @var FinishFlow $useCase */
            $useCase = app()->makeWith(FinishFlow::class, ["telegram" => $this->telegram]);
            $useCase->perform(false);

            return;
        }

        $this->telegram->sendMessage("Desculpe pelo erro, por favor tente novamente!");

        /** @var FinishFlow $useCase */
        $useCase = app()->makeWith(FinishFlow::class, ["telegram" => $this->telegram]);
        $useCase->perform();
    }
}
