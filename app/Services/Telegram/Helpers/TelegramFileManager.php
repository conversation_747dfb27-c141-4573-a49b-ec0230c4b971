<?php

namespace App\Services\Telegram\Helpers;

use App\Services\Telegram\Telegram;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Telegram\Bot\Exceptions\TelegramSDKException;

class TelegramFileManager
{
    public const string TELEGRAM_FILE_URL = "https://api.telegram.org/file/bot";

    /**
     * @param Telegram $service
     * @param mixed $fileId
     * @throws TelegramSDKException
     * @throws Exception
     *
     * @return UploadedFile
     */
    public static function getTelegramUploadedFile(Telegram $service): UploadedFile
    {
        $fileInfo = $service->api->getFile(['file_id' => $service->file_id]);
        $filePath = $fileInfo->file_path;
        $fileUrl = self::TELEGRAM_FILE_URL . env("TELEGRAM_BOT_TOKEN") . "/{$filePath}";

        $response = Http::get($fileUrl);
        if (!$response->successful()) {
            throw new Exception("Failed to download file from Telegram");
        }

        $tempPath = storage_path('app/temp/' . basename($filePath));

        Storage::makeDirectory('temp');

        file_put_contents($tempPath, $response->body());

        return new UploadedFile(
            $tempPath,
            basename($filePath),
            $response->header('Content-Type'),
            null, // Error status (null means no errors)
            true // Set test mode to avoid move() issues
        );
    }

}
