<?php

namespace App\Services\Telegram;

use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use App\Domains\Organization;
use App\Helpers\DBLog;
use App\Services\Telegram\Domains\Messages\Begin;
use App\Services\Telegram\Domains\Messages\Error\GenericError;
use App\Services\Telegram\Domains\Messages\RequestRegister;
use App\Services\Telegram\Domains\TelegramBot;
use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Domains\TelegramFile;
use App\Services\Telegram\Domains\TelegramMessage;
use App\Services\Telegram\Domains\TelegramUser;
use App\Services\Telegram\UseCases\GetChat;
use App\Services\Telegram\UseCases\ResetChat;
use App\Services\Telegram\UseCases\RunCommand;
use App\Services\Telegram\UseCases\CustomFlow\Run as CustomRun;
use App\Services\Telegram\UseCases\StoreMessage;
use App\Services\Telegram\UseCases\TelegramFile\Upload;
use App\Services\Telegram\UseCases\TelegramUser\GetUserByTelegramID;
use App\Domains\User as UserDomain;
use App\Domains\Inventory\Client;
use App\Services\Telegram\UseCases\UpdateChat;
use App\UseCases\ChatBot\Flow\GetDefault;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Api;
use Telegram\Bot\Exceptions\TelegramSDKException;
use Telegram\Bot\FileUpload\InputFile;
use Telegram\Bot\Keyboard\Keyboard;
use Telegram\Bot\Objects\CallbackQuery;
use Telegram\Bot\Objects\Chat;
use Telegram\Bot\Objects\Document;
use Telegram\Bot\Objects\Message;
use Telegram\Bot\Objects\PhotoSize;
use Telegram\Bot\Objects\Update;
use Telegram\Bot\Objects\User;

class Telegram
{

    private const string TELEGRAM_BOT_ID = "telegram.bot.id";
    private const string TELEGRAM_BOT_TOKEN = "telegram.bot.token";
    private const string TELEGRAM_API_URL = "telegram.api.url";

    private const string PROCESSING_MESSAGE = 'Processando..';

    private const array VALID_FLOWS = [
        "read_doc_text" => UseCases\Flows\ReadDocText\RunCurrentStatus::class,
    ];

    public ?User $bot;
    public ?TelegramBot $telegramBot;
    public string $bot_id;
    public ?int $telegram_bot_id;
    public ?int $chat_id;
    public ?int $telegram_id;
    public ?string $file_id;
    public ?Document $document;
    public ?PhotoSize $photoSize;
    public string $api_token;
    public Api $api;
    public ?Update $update;
    public ?Message $message;
    public ?CallbackQuery $callbackQuery;
    public ?Chat $chat;

    public ?User $from;
    public ?TelegramChat $telegramChat;
    public ?TelegramMessage $telegramMessage;
    public ?TelegramUser $telegramUser;
    public ?UserDomain $user;
    public ?Organization $organization;
    public ?TelegramFile $telegramFile;
    public ?Client $client;

    public ?string $text;
    public ?string $callbackData;

    public bool $isCurrentlyOnFlow = false;
    public bool $isCurrentlyOnCustomFlow = false;
    public ?string $currentFlow = null;
    public ?string $currentFlowStatus = null;
    public ?Flow $customCurrentFlow = null;
    public bool $is_custom_bot = false;

    public ?int $currentStepID = null;
    public ?Step $currentStep = null;

    /**
     * @throws TelegramSDKException
     */
    public function __construct(?TelegramBot $bot = null) {
        $this->telegramBot = $bot;
        $this->telegram_bot_id = $bot?->id;
        $this->bot_id = $bot?->telegram_id ?? config(self::TELEGRAM_BOT_ID);
        $this->api_token = $bot?->token ?? config(self::TELEGRAM_BOT_TOKEN);
        $this->api = new Api($this->api_token);
        $this->bot = $this->api->getMe();
        $this->is_custom_bot = $bot?->organization_id !== null;
    }

    /**
     * @throws TelegramSDKException
     */
    public function setWebhook(bool $custom_url = false) : string {
        $webhookUrl = config(self::TELEGRAM_API_URL);
        if ($custom_url && $this->telegramBot && $this->telegramBot->bot) {
            $webhookUrl = str_replace('{bot_id}', $this->telegramBot->bot, config('telegram.api.custom_url'));
        }

        $this->api->setWebhook([
            'url' => $webhookUrl
        ]);
        return $webhookUrl;
    }

    public function receiveMessage() : bool {
        $this->update = $this->api->getWebhookUpdate();

        $this->message = $this->update->message;
        $this->callbackQuery = $this->update->callbackQuery;

        if(!$this->message && !$this->callbackQuery) {
            return false;
        }
        $this->text = $this->update->message?->text;
        if(!$this->message && $this->callbackQuery) {
            $this->setCallBackData();
        }

        try {
            $this->chat = $this->message->chat;
            $this->from = $this->callbackQuery->from ?? $this->message->from;

            $this->chat_id = $this->chat->id;
            $this->telegram_id = $this->from->id;

            $this->setUser();
            $this->setDocumentAndPhoto();
            $this->setTelegramChat();
            $this->setTelegramMessage();

            if ($this->is_custom_bot && $this->isCurrentlyOnCustomFlow) {
                return $this->customRun();
            }

            $this->runThroughFlow();
        } catch (\Throwable $e){
            if(!empty($this->telegramChat)){
                $this->resetChat();

                new GenericError($this);
            }
            throw $e;
        }
        return true;
    }

    private function customRun() : bool {
        /** @var CustomRun $useCase */
        $useCase = app()->makeWith(CustomRun::class, ["telegram" => $this]);
        return $useCase->perform();
    }

    private function setDocumentAndPhoto(): void {
        if (!$this->update?->message?->document && !$this->update?->message?->photo) {
            return;
        }

        $this->document = $this->update?->message?->document;

        $photo = $this->update?->message?->photo;
        if ($photo) {
            $this->photoSize = (is_array($photo)) ? last($photo) : $photo->last();
        }

        $this->file_id = ($this->document) ? $this->document->fileId : $this->photoSize->fileId;

        /** @var Upload $useCase */
        $useCase = app()->make(Upload::class);
        $this->telegramFile = $useCase->perform($this);
    }

    private function setCallBackData() : void {
        $this->message = $this->callbackQuery->message;
        $this->text = $this->callbackQuery->data;
        $this->callbackData = $this->callbackQuery->data;
        $this->api->answerCallbackQuery([
            'callback_query_id' => $this->callbackQuery->id,
            'text' => self::PROCESSING_MESSAGE,
            'show_alert' => false,
        ]);
    }

    public function resetChat() : void {
        /** @var ResetChat $useCase */
        $useCase = app()->make(ResetChat::class);
        $useCase->perform($this->telegramChat);
    }

    public function runThroughFlow() : void {
        $this->log("[Telegram::runThroughFlow]", [$this->message->text]);

        $hasNoUser = $this->hasNoUser();

        $this->shouldResetChat();

        $internalFlow = $this->runInternalFlow();

        $command = new RunCommand($this);
        $hasCommand = $command->run();

        if(!$hasCommand && !$hasNoUser && !$internalFlow) {
            new Begin($this);
        }
        $this->log("[Telegram:runThroughFlow::Finished]");
    }

    private function runInternalFlow() : bool {
        $this->log("[Telegram::runInternalFlow]", [$this->currentFlow]);
        try{
            if ($this->isCurrentlyOnFlow){
                $flowClass = self::VALID_FLOWS[$this->currentFlow];
                $flow = app()->makeWith($flowClass, ['telegram' => $this]);
                $flow->run();
                return true;
            }
        } catch (\Throwable $e){
            $this->log("[RunInternalFlow::Possible_Invalid_currentFlow]", [$this->currentFlow]);
            $this->log($e->getMessage(), [], true);
        }
        return false;
    }

    private function shouldResetChat() : void {
        if($this->telegramChat->has_broken_flow) {
            $this->log("[Telegram::shouldResetChat: Flow is broken, resetting flow]", [
                "chat_id" => $this->chat_id, "telegram_id" => $this->telegram_id
            ]);
            $this->resetChat();
        }
    }

    private function hasNoUser() : bool {
        $hasNoUser = (!$this->user || !$this->organization || !$this->telegramUser);

        if($hasNoUser) {
            $this->log("[Telegram::runThroughFlow::noUser]", [
                "chat_id" => $this->chat_id, "telegram_id" => $this->telegram_id
            ]);
            new RequestRegister($this);
        }

        return $hasNoUser;
    }

    /**
     * @throws TelegramSDKException
     */
    public function sendMessage(string $text, ?Keyboard $keyboard = null) : Message {
        $message = [
            'chat_id' => $this->chat->id,
            'text' => $text
        ];
        if($keyboard){
            $message['reply_markup'] = $keyboard;
        }
        return $this->api->sendMessage($message);
    }

    /**
     * @throws TelegramSDKException
     */
    public function sendDocument(InputFile $file, string $caption = 'report'): Message
    {
        return $this->api->sendDocument([
            'chat_id' => $this->chat->id,
            'document' => $file,
            'caption' => $caption,
        ]);
    }

    /**
     * @throws BindingResolutionException
     */
    private function setUser() : void {
        /** @var GetUserByTelegramID $useCase */
        $useCase = app()->make(GetUserByTelegramID::class);
        $this->telegramUser = $useCase->perform($this->telegram_id);

        $this->client = $this->telegramUser?->client;
        $this->user = $this->telegramUser?->user;
        $this->organization = $this->telegramBot->organization ?? $this->telegramUser?->user->organization;
    }

    private function setTelegramChat() : void {
        $this->log("[Telegram::setTelegramChat]", ["chat_id" => $this->chat->id, "from_id" => $this->from->id]);

        /** @var GetChat $useCase */
        $useCase = app()->make(GetChat::class);
        $this->telegramChat = $useCase->perform($this);

        $this->isCurrentlyOnFlow = $this->telegramChat->has_active_flow;
        $this->currentFlow = $this->telegramChat->current_flow;
        $this->currentFlowStatus = $this->telegramChat->current_flow_status;
        $this->setFlow();
    }

    private function setFlow() : void {
        if (!$this->is_custom_bot) {
            return;
        }
        $this->customCurrentFlow = $this->telegramChat->flow;
        $this->isCurrentlyOnCustomFlow = $this->customCurrentFlow ?? false;
        $this->currentStepID = $this->telegramChat->current_step_id;

        if (!$this->isCurrentlyOnCustomFlow && $this->organization) {
            /** @var GetDefault $useCase */
            $useCase = app()->make(GetDefault::class);
            $defaultFlow = $useCase->perform($this->organization->id);
            if ($defaultFlow) {
                $this->customCurrentFlow = $defaultFlow;
                $this->isCurrentlyOnCustomFlow = true;
                $this->currentStep = $defaultFlow->getInitialStep();
                $this->currentStepID = $this->currentStep->id;

                $this->telegramChat->flow = $defaultFlow;
                $this->telegramChat->current_flow_id = $defaultFlow->id;
                $this->telegramChat->step = $this->currentStep;
                $this->telegramChat->current_step_id = $this->currentStep->id;

                /** @var UpdateChat $updateChatUseCase */
                $updateChatUseCase = app()->make(UpdateChat::class);
                $updateChatUseCase->perform($this->telegramChat);
            }
        }
    }

    private function setTelegramMessage() : void {
        $this->log("[Telegram::setTelegramMessage]", [
            "message_text" => $this->message?->text, "callback_data" => $this->callbackQuery?->data
        ]);

        /** @var StoreMessage $useCase */
        $useCase = app()->make(StoreMessage::class);
        $this->telegramMessage = $useCase->perform($this->update, $this->telegramChat);
    }

    public function log(?string $message, ?array $log = [], bool $error = false) : void {
        if($error){
            Log::error($message, $log);
            DBLog::logError(
                $message ?? "","telegram",$this->organization?->id ?? null,$this->user?->id ?? null,$log ?? null,
            );
            return;
        }
        Log::info($message, $log);
        DBLog::log(
            $message ?? "",
            "telegram",
            $this->organization?->id ?? null,
            $this->user?->id ?? null,
            $log ?? null,
        );
    }
}
