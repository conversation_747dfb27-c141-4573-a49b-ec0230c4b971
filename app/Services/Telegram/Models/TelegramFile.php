<?php

namespace App\Services\Telegram\Models;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TelegramFile extends Model
{
    use SoftDeletes;

    protected $table = 'telegram_files';

    protected $fillable = [
        'organization_id',
        'user_id',
        'bot_id',
        'chat_id',
        'from_id',
        'file',
        'filename',
        'filesize',
        'filepath',
        'file_extension',
        'file_mime_type',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function user(){
        return $this->belongsTo(User::class);
    }
    public function chat(){
        return $this->belongsTo(TelegramChat::class);
    }
}
