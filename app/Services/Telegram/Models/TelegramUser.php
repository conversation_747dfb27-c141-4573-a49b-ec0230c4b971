<?php

namespace App\Services\Telegram\Models;

use App\Models\User;
use App\Models\Client;
use Illuminate\Database\Eloquent\Model;

class TelegramUser extends Model
{
    protected $table = "telegram_users";

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'client_id',
        'telegram_id',
        'description',
    ];

    public function user() {
        return $this->belongsTo(User::class);
    }

    public function client() {
        return $this->belongsTo(Client::class);
    }
}
