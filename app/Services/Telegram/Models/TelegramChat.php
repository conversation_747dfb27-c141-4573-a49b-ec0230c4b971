<?php

namespace App\Services\Telegram\Models;

use App\Models\Flow;
use App\Models\Organization;
use App\Models\Step;
use App\Models\User;
use App\Services\Telegram\Models\TelegramUser;
use Illuminate\Database\Eloquent\Model;

class TelegramChat extends Model
{
    protected $table = "telegram_chats";

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'organization_id',
        'bot_id',
        'user_id',
        'telegram_user_id',
        'chat_id',
        'from_id',
        'has_active_flow',
        'has_broken_flow',
        'current_flow',
        'current_flow_id',
        'current_flow_status',
        'current_step_id',
        'ocr_raw',
        'ocr_data',
    ];

    public function user() {
        return $this->belongsTo(User::class);
    }

    public function organization() {
        return $this->belongsTo(Organization::class);
    }

    public function messages() {
        return $this->hasMany(TelegramMessage::class, 'telegram_chat_id');
    }

    public function flow() {
        return $this->belongsTo(Flow::class, 'current_flow_id');
    }

    public function step() {
        return $this->belongsTo(Step::class, 'current_step_id');
    }

    public function telegramUser() {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }
}
