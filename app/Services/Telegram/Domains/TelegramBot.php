<?php

namespace App\Services\Telegram\Domains;

use App\Domains\Organization;
use App\Enums\TelegramBotStatus;
use App\Enums\TelegramBotPublishingStatus;
use Carbon\Carbon;

class TelegramBot
{
    public ?int $id;
    public ?int $organization_id;
    public ?Organization $organization;
    public ?string $telegram_id;
    public ?string $bot;
    public ?string $token;
    public ?string $url;
    public ?string $name;
    public ?string $description;
    public ?bool $is_active;
    public ?bool $is_published;
    public ?TelegramBotStatus $status;
    public ?TelegramBotPublishingStatus $publishing_status;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?string $telegram_id,
        ?string $bot,
        ?string $token,
        ?string $url,
        ?string $name,
        ?string $description,
        ?bool $is_active,
        ?bool $is_published = false,
        ?TelegramBotStatus $status = null,
        ?TelegramBotPublishingStatus $publishing_status = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Organization $organization = null
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->telegram_id = $telegram_id;
        $this->bot = $bot;
        $this->token = $token;
        $this->url = $url;
        $this->name = $name;
        $this->description = $description;
        $this->is_active = $is_active;
        $this->is_published = $is_published;
        $this->status = $status ?? TelegramBotStatus::is_draft;
        $this->publishing_status = $publishing_status ?? TelegramBotPublishingStatus::is_draft;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->organization = $organization;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "telegram_id" => $this->telegram_id,
            "bot" => $this->bot,
            "token" => $this->token,
            "url" => $this->url,
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_published" => $this->is_published,
            "status" => $this->status->value,
            "publishing_status" => $this->publishing_status->value,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "organization" => $this->organization?->toArray()
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "telegram_id" => $this->telegram_id,
            "bot" => $this->bot,
            "token" => $this->token,
            "url" => $this->url,
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_published" => $this->is_published,
            "status" => $this->status->value,
            "publishing_status" => $this->publishing_status->value,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "telegram_id" => $this->telegram_id,
            "bot" => $this->bot,
            "token" => $this->token,
            "url" => $this->url,
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_published" => $this->is_published,
            "status" => $this->status->value,
            "publishing_status" => $this->publishing_status->value,
        ];
    }
}
