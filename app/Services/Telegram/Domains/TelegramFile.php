<?php

namespace App\Services\Telegram\Domains;

use App\Helpers\File;
use App\Helpers\Traits\FileDomain;
use Carbon\Carbon;

class TelegramFile
{
    use FileDomain;

    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;
    public ?string $bot_id;
    public ?int $chat_id;
    public ?int $from_id;

    public ?string $file;
    public ?string $filename;
    public ?string $filepath;
    public ?int $filesize;
    public ?string $file_extension;
    public ?string $file_mime_type;
    public ?File $fileHelper;

    public const FILEPATH = "telegram/files/";

    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $user_id,
        ?string $bot_id,
        ?int $chat_id,
        ?int $from_id,
        ?string $file,
        ?string $filename,
        ?string $filepath,
        ?int $filesize,
        ?string $file_extension,
        ?string $file_mime_type,
        ?File $fileHelper = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->bot_id = $bot_id;
        $this->chat_id = $chat_id;
        $this->from_id = $from_id;
        $this->file = $file;
        $this->filename = $filename;
        $this->filepath = $filepath;
        $this->filesize = $filesize;
        $this->file_extension = $file_extension;
        $this->file_mime_type = $file_mime_type;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->fileHelper = $fileHelper;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "bot_id" => $this->bot_id,
            "chat_id" => $this->chat_id,
            "from_id" => $this->from_id,
            "file" => $this->file,
            "filename" => $this->filename,
            "filepath" => $this->filepath,
            "filesize" => $this->filesize,
            "file_extension" => $this->file_extension,
            "file_mime_type" => $this->file_mime_type,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s")
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "bot_id" => $this->bot_id,
            "chat_id" => $this->chat_id,
            "from_id" => $this->from_id,
            "file" => $this->file,
            "filename" => $this->filename,
            "filepath" => $this->filepath,
            "filesize" => $this->filesize,
            "file_extension" => $this->file_extension,
            "file_mime_type" => $this->file_mime_type,
        ];
    }

    public function toUpdateArray(): array
    {
        return [];
    }
}
