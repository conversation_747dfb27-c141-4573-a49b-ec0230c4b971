<?php

namespace App\Services\Telegram\Domains;

class TelegramMessage
{
    public ?int $id;
    public int $telegram_chat_id;
    public ?string $text;
    public ?bool $is_callback_data;

    public function __construct(
        ?int $id,
        int $telegram_chat_id,
        ?string $text = null,
        ?bool $is_callback_data = false
    ){
        $this->id = $id;
        $this->telegram_chat_id = $telegram_chat_id;
        $this->text = $text;
        $this->is_callback_data = $is_callback_data;
    }

    public function toArray() : array {
        return [
            "id" => $this->id,
            "telegram_chat_id" => $this->telegram_chat_id,
            "text" => $this->text,
            "is_callback_data" => $this->is_callback_data,
        ];
    }
    public function toStoreArray() : array {
        return [
            "telegram_chat_id" => $this->telegram_chat_id,
            "text" => $this->text,
            "is_callback_data" => $this->is_callback_data,
        ];
    }
    public function toUpdateArray() : array {
        return $this->toStoreArray();
    }
}
