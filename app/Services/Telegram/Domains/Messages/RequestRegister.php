<?php

namespace App\Services\Telegram\Domains\Messages;

use App\Services\Telegram\Telegram;

class RequestRegister
{
    public const SEARCH_ARRAY = [
        "{{username}}",
        "{{userid}}"
    ];

    public const MESSAGE = "Ol<PERSON> {{username}}! Seja bem vindo(a) ao FlechAI o bot inteligente da Flecha!\n\n".
                           "Pelo que pudemos ver aqui você não possui um usuário associado ao telegram no flecha! ".
                           "Faça seu cadastre no useflecha.com.br, associe o ID do seu telegram que você pode".
                           " ver abaixo e comece a utilizar o nosso bot inteligente!\n\n".
                           "Seu id do telegram é: {{userid}}\n";

    public function __construct(Telegram $service) {
        $message = self::MESSAGE;

        $service->sendMessage(str_replace(
            self::SEARCH_ARRAY,
            [$service->from->firstName, $service->from->id],
            $message
        ));
    }
}
