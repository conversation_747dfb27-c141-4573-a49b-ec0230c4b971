<?php

namespace App\Services\Telegram\Domains\Messages\Inventory;

use App\Services\Telegram\Telegram;
use Telegram\Bot\Keyboard\Keyboard;

class BeginInventory
{


    public const MESSAGE = "Bem vindo(a) ao módulo de inventário, onde você pode gerir e consultar seus estoques!\n".
                           "Selecione uma opção no menu\n";

    public function __construct(Telegram $service) {
        $message = self::MESSAGE;

        $keyboard = Keyboard::make()
            ->inline()
            ->row([Keyboard::inlineButton(['text' => 'Consulta de Estoque', 'callback_data' => 'stock'])])
            ->row([Keyboard::inlineButton(['text' => 'Entrada de Estoque', 'callback_data' => 'stock_entry'])])
            ->row([Keyboard::inlineButton(['text' => 'Saída de Estoque', 'callback_data' => 'stock_exit'])])
            ->row([Keyboard::inlineButton(['text' => 'Dados e Relatórios', 'callback_data' => 'inventory_data'])])
            ->row([Keyboard::inlineButton(['text' => 'Voltar', 'callback_data' => 'back_to_begin'])]);

        $service->sendMessage($message, $keyboard ?? null);
    }
}
