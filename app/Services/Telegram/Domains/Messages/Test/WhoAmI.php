<?php

namespace App\Services\Telegram\Domains\Messages\Test;

use App\Services\Telegram\Telegram;

class WhoAmI
{
    public const SEARCH_ARRAY = [
        "{{username}}",
        "{{lastname}}",
        "{{userid}}"
    ];

    public const MESSAGE = "<PERSON><PERSON><PERSON> {{username}}!\n".
                           "Seu id é {{userid}}\n".
                           "e seu sobrenome é {{lastname}}";

    public function __construct(Telegram $service) {
        $message = self::MESSAGE;

        $service->sendMessage(str_replace(
            self::SEARCH_ARRAY,
            [$service->from->firstName, $service->from->lastName, $service->from->id],
            $message
        ));
    }
}
