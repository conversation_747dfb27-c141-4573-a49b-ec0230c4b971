<?php

namespace App\Services\Tesseract\Factories;


use App\Helpers\File;
use App\Http\Requests\RawImage\ReadRequest;
use App\Services\Tesseract\Domains\RawImage;
use App\Services\Tesseract\Tesseract;

class RawImageFactory
{
    public Tesseract $tesseract;
    public function __construct(Tesseract $tesseract){
        $this->tesseract = $tesseract;
    }

    public function buildFromFile(File $file, ReadRequest $request): RawImage {
        return new RawImage(
            $request->organization_id ?? null,
            $request->user_id ?? null,
            null,
            null,
            null,
            null,
            null,
            null,
            $file,
            $this->tesseract ?? null,
        );
    }
}
