<?php

namespace App\Services\Tesseract\Domains;

use App\Domains\Imports\Header;
use App\Domains\Imports\ImportBrand;
use App\Domains\Imports\ImportBudget;
use App\Domains\Imports\ImportClient;
use App\Domains\Imports\ImportGroup;
use App\Domains\Imports\ImportProduct;
use App\Domains\Imports\ImportProject;
use App\Domains\Imports\ImportStock;
use App\Domains\Imports\ImportStockEntry;
use App\Domains\Imports\ImportStockExit;
use App\Helpers\File;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class Image
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;

    public ?string $model;
    public ?string $status;
    public ?string $header;
    public ?string $map;
    public ?bool $is_processed;
    public ?string $file;
    public ?string $filename;
    public ?string $filepath;
    public ?int $filesize;
    public ?string $file_extension;
    public ?string $file_mime_type;
    public ?File $fileHelper;

    public const FILEPATH = "imports/";

    public Header $headerRow;

    public bool $skipHeader = false;
    public bool $reprocess = false;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $user_id,
        ?string $model,
        ?string $status,
        ?string $header,
        ?string $map,
        bool $is_processed,
        ?string $file,
        ?string $filename,
        ?string $filepath,
        ?int $filesize,
        ?string $file_extension,
        ?string $file_mime_type,
        ?File $fileHelper = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->model = $model;
        $this->status = $status;
        $this->header = $header;
        $this->map = $map;
        $this->is_processed = $is_processed;
        $this->file = $file;
        $this->filename = $filename;
        $this->filepath = $filepath;
        $this->filesize = $filesize;
        $this->file_extension = $file_extension;
        $this->file_mime_type = $file_mime_type;
        $this->fileHelper = $fileHelper;

        $this->headerRow = new Header();
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "model" => $this->model,
            "status" => $this->status,
            "header" => $this->header,
            "map" => $this->map,
            "is_processed" => $this->is_processed,
            "file" => $this->file,
            "filename" => $this->filename,
            "filepath" => $this->filepath,
            "filesize" => $this->filesize,
            "file_extension" => $this->file_extension,
            "file_mime_type" => $this->file_mime_type,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "model" => $this->model,
            "status" => $this->status,
            "header" => $this->header,
            "map" => $this->map,
            "is_processed" => $this->is_processed,
            "file" => $this->file,
            "filename" => $this->filename,
            "filepath" => $this->filepath,
            "filesize" => $this->filesize,
            "file_extension" => $this->file_extension,
            "file_mime_type" => $this->file_mime_type,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "model" => $this->model,
            "status" => $this->status,
            "map" => $this->map,
            "is_processed" => $this->is_processed,
        ];
    }

    public function upload() : void {
        $meta = $this->fileHelper->getMeta();

        $this->filepath = self::FILEPATH . $this->organization_id;
        $this->filename = $meta["originalName"];
        $this->filesize = $meta["size"];
        $this->file_extension = $meta["originalExtension"];
        $this->file_mime_type = $meta["mimeType"];

        $this->file = $this->fileHelper->upload(self::FILEPATH . $this->organization_id);
    }

    public function getFilePath() : string {
        return Storage::disk('public')->path($this->file);
    }

    public function getHeader(): void {
        Excel::import($this->headerRow, $this->getFilePath());

        $this->header = $this->headerRow->toJson();
    }

    public function getMap(): void {
        $import = $this->getModelImport();
        $import->getMap();

        $this->map = $import->toJson();
    }

    public function getModelImport() : ImportProduct|null {
        return match ($this->model) {
            "products" => new ImportProduct(),
            "brands" => new ImportBrand(),
            "groups" => new ImportGroup(),
            "clients" => new ImportClient(),
            "projects" => new ImportProject(),
            "budgets" => new ImportBudget(),
            "stocks" => new ImportStock(),
            "stock_entries" => new ImportStockEntry(),
            "stock_exits" => new ImportStockExit(),
            default => null,
        };
    }

    public function process() : void {
        $map = json_decode($this->map, true);

        $import = $this->getModelImport();
        $import->organization_id = $this->organization_id;
        $import->skipHeader = $this->skipHeader;
        $import->setMap($map);

        Excel::import($import, $this->getFilePath());
    }
}
