<?php

namespace App\Services\Tesseract\Domains;

use App\Helpers\File;
use App\Helpers\Traits\FileDomain;
use App\Services\Tesseract\Tesseract;


class RawImage
{
    use FileDomain;

    public ?int $organization_id;
    public ?int $user_id;

    public ?string $file;
    public ?string $filename;
    public ?string $filepath;
    public ?int $filesize;
    public ?string $file_extension;
    public ?string $file_mime_type;
    public ?File $fileHelper;
    public ?Tesseract $tesseract;

    public ?string $text;

    public const string FILEPATH = "imports/";

    public function __construct(
        ?int $organization_id,
        ?int $user_id,
        ?string $file,
        ?string $filename,
        ?string $filepath,
        ?int $filesize,
        ?string $file_extension,
        ?string $file_mime_type,
        ?File $fileHelper = null,
        ?Tesseract $tesseract = null,
    ){
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->file = $file;
        $this->filename = $filename;
        $this->filepath = $filepath;
        $this->filesize = $filesize;
        $this->file_extension = $file_extension;
        $this->file_mime_type = $file_mime_type;
        $this->fileHelper = $fileHelper;
        $this->tesseract = $tesseract;
    }

    public function toArray(): array
    {
        return [
            "text" => $this->text,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "file" => $this->file,
            "filename" => $this->filename,
            "filepath" => $this->filepath,
            "filesize" => $this->filesize,
            "file_extension" => $this->file_extension,
            "file_mime_type" => $this->file_mime_type,
        ];
    }

    public function toSimplifiedArray(): array
    {
        return [
            "text" => $this->text,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
        ];
    }

    public function process() : void {
        $this->tesseract->setLang("por");
        $this->tesseract->loadRaw(
            $this->getRealPath()
        );

        $this->text = $this->tesseract->text;
    }
}
