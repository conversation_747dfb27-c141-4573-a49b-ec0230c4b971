<?php

namespace App\Services\Meta\WhatsApp;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Template;
use App\Services\Meta\WhatsApp\Domains\WhatsAppTemplate;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppTemplateRepository;
use App\Services\Meta\WhatsApp\Factories\WhatsAppTemplateFactory;
use GuzzleHttp\Exception\GuzzleException;
use Exception;

class TemplateService extends WhatsAppService
{
    protected WhatsAppTemplateRepository $repository;
    protected WhatsAppTemplateFactory $factory;

    public function __construct(
        WhatsAppTemplateRepository $repository,
        WhatsAppTemplateFactory $factory,
        ?PhoneNumber $phoneNumber = null
    ) {
        parent::__construct($phoneNumber);
        $this->endpoint = "message_templates";
        $this->repository = $repository;
        $this->factory = $factory;
    }

    /**
     * Register a new WhatsApp template: API call + save locally.
     *
     * @param Template $template
     * @return WhatsAppTemplate
     * @throws Exception|GuzzleException
     */
    public function register(Template $template) : WhatsAppTemplate {
        if(!$template->validateForWhatsApp()){
            throw new Exception("Template is not valid for WhatsApp");
        }

        $response = $this->post(
            $template->toWhatsAppPayload()
        );
        $responseData = json_decode(
            (string) $response->getBody(), true
        );

        $status = $responseData['status'] ?? null;
        $external_id = $responseData['id'] ?? null;

        $whatsAppTemplate = $this->factory->buildFromTemplate(
            $template,
            $status,
            $external_id,
            json_encode($responseData)
        );

        return $this->repository->save($whatsAppTemplate);
    }
}
