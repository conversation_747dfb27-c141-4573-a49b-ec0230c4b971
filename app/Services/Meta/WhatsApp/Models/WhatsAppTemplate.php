<?php

namespace App\Services\Meta\WhatsApp\Models;

use App\Models\Template;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WhatsAppTemplate extends Model
{
    use SoftDeletes;

    protected $table = 'whatsapp_templates';

    protected $fillable = [
        'template_id',
        'status',
        'external_id',
        'json',
    ];

    public function template()
    {
        return $this->belongsTo(Template::class);
    }
}
