<?php

namespace App\Services\Meta\WhatsApp\Factories;

use App\Domains\ChatBot\Template;
use App\Factories\ChatBot\TemplateFactory;
use App\Services\Meta\WhatsApp\Domains\WhatsAppTemplate;
use App\Services\Meta\WhatsApp\Models\WhatsAppTemplate as WhatsAppTemplateModel;

class WhatsAppTemplateFactory
{
    private TemplateFactory $templateFactory;

    public function __construct(TemplateFactory $templateFactory)
    {
        $this->templateFactory = $templateFactory;
    }

    public function buildFromModel(?WhatsAppTemplateModel $whatsappTemplate): ?WhatsAppTemplate
    {
        if (!$whatsappTemplate) {
            return null;
        }

        $template = null;
        if ($whatsappTemplate->template) {
            $template = $this->templateFactory->buildFromModel($whatsappTemplate->template);
        }

        return new WhatsAppTemplate(
            $whatsappTemplate->id ?? null,
            $whatsappTemplate->template_id ?? null,
            $template,
            $whatsappTemplate->status ?? null,
            $whatsappTemplate->external_id ?? null,
            $whatsappTemplate->json ?? null,
            $whatsappTemplate->created_at ?? null,
            $whatsappTemplate->updated_at ?? null
        );
    }

    public function buildFromTemplate(
        Template $template,
        ?string $status = null,
        ?string $external_id = null,
        ?string $json = null
    ): WhatsAppTemplate {
        return new WhatsAppTemplate(
            null,
            $template->id,
            $template,
            $status,
            $external_id,
            $json
        );
    }
}
