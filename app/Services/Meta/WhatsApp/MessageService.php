<?php

namespace App\Services\Meta\WhatsApp;

use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Repositories\MessageRepository;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppTemplateRepository;
use GuzzleHttp\Exception\GuzzleException;
use Exception;
// CHECK THIS
class MessageService extends WhatsAppService
{
    protected MessageRepository $messageRepository;
    protected WhatsAppTemplateRepository $whatsAppTemplateRepository;

    public function __construct(
        MessageRepository $messageRepository,
        WhatsAppTemplateRepository $whatsAppTemplateRepository,
        ?PhoneNumber $phoneNumber = null
    ) {
        parent::__construct($phoneNumber);
        $this->endpoint = "messages";
        $this->messageRepository = $messageRepository;
        $this->whatsAppTemplateRepository = $whatsAppTemplateRepository;
    }

    /**
     * Send a WhatsApp message
     *
     * @param Message $message
     * @return void
     * @throws Exception|GuzzleException
     */
    public function send(Message $message): array
    {
        $whatsAppTemplate = $this->whatsAppTemplateRepository->fetchByTemplateId($message->template_id);
        if (!$whatsAppTemplate || !$whatsAppTemplate->external_id) {
            throw new Exception("Template is not published to WhatsApp");
        }

        // Get the client phone number
        if (!$message->client || !$message->client->phone) {
            throw new Exception("Client has no phone number");
        }

        // Prepare payload for WhatsApp API
        $payload = $message->toWhatsAppPayload();

        // Send a message to WhatsApp API
        $response = $this->post($payload);
        $responseData = json_decode((string) $response->getBody(), true);

        return $responseData;
    }
}
