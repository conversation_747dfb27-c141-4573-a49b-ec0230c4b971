<?php

namespace App\Services\Meta\WhatsApp\Domains;

use App\Domains\ChatBot\Template;
use Carbon\Carbon;

class WhatsAppTemplate
{
    public ?int $id;
    public ?int $template_id;
    public ?Template $template;
    public ?string $status;
    public ?string $external_id;
    public ?string $json;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public function __construct(
        ?int $id,
        ?int $template_id,
        ?Template $template = null,
        ?string $status = null,
        ?string $external_id = null,
        ?string $json = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null
    ) {
        $this->id = $id;
        $this->template_id = $template_id;
        $this->template = $template;
        $this->status = $status;
        $this->external_id = $external_id;
        $this->json = $json;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "template_id" => $this->template_id,
            "template" => $this->template?->toArray(),
            "status" => $this->status,
            "external_id" => $this->external_id,
            "json" => $this->json,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "template_id" => $this->template_id,
            "status" => $this->status,
            "external_id" => $this->external_id,
            "json" => $this->json,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "template_id" => $this->template_id,
            "status" => $this->status,
            "external_id" => $this->external_id,
            "json" => $this->json,
        ];
    }
}
