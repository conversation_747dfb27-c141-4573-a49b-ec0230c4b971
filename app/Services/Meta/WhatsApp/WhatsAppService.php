<?php

namespace App\Services\Meta\WhatsApp;

use App\Domains\ChatBot\PhoneNumber;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;

class WhatsAppService
{
    protected Client $client;
    protected string $apiUrl;
    protected string $phoneNumberId;
    protected string $token;
    protected string $endpoint;

    public function __construct(?PhoneNumber $phoneNumber = null)
    {
        $this->client = new Client();
        $this->apiUrl = config('whatsapp.base_url');
        $this->phoneNumberId = $phoneNumber?->whatsapp_phone_number_id ?? config('whatsapp.phone_number_id');
        $this->token = $phoneNumber?->whatsapp_access_token ?? config('whatsapp.access_token');
    }

    /**
     * @throws GuzzleException
     */
    protected function post(array $payload, ?string $endpoint = null) : ResponseInterface {
        $endpoint = $endpoint ?? $this->endpoint;

        $url = "{$this->apiUrl}/{$this->phoneNumberId}/{$endpoint}";

        return $this->client->post($url, [
            'headers' => [
                'Authorization' => "Bearer {$this->token}",
                'Content-Type' => 'application/json',
            ],
            'json' => $payload,
        ]);
    }
}
