<?php

namespace App\Factories;

use App\Domains\Log;
use App\Models\Log as LogModel;

class LogFactory
{

    public function buildFromArray(array $log) : Log {
        return new Log(
            null,
            $log['organization_id'] ?? null,
            $log['user_id'] ?? null,
            $log['is_error'] ?? null,
            $log['from'] ?? null,
            $log['message'] ?? null,
            $log['log'] ?? null,
        );
    }

    public function buildFromModel(?LogModel $log) : ?Log {
        if(!$log){ return null; }

        return new Log(
            $log->id ?? null,
            $log->organization_id ?? null,
            $log->user_id ?? null,
            $log->is_error ?? null,
            $log->from ?? null,
            $log->message ?? null,
            $log->log ?? null,
            $log->created_at ?? false,
            $log->updated_at ?? "",
        );
    }
}
