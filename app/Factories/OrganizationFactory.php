<?php

namespace App\Factories;

use App\Domains\Organization;
use App\Models\Organization as OrganizationModel;

class OrganizationFactory
{
    public function buildFromStoreArray(?array $request) : ?Organization {
        if(!$request){
            return null;
        }

        return new Organization(
            null,
            $request['name'] ?? "",
            $request['description'] ?? "",
            true,
            false
        );
    }

    public function buildFromModel(?OrganizationModel $organization) : ?Organization {
        if(!$organization){
            return null;
        }
        return new Organization(
            $organization->id ?? null,
            $organization->name ?? "",
            $organization->description ?? "",
            $organization->is_active ?? null,
            $organization->is_suspended ?? false,
            $organization->created_at ?? null,
            $organization->updated_at ?? null,
        );
    }
}
