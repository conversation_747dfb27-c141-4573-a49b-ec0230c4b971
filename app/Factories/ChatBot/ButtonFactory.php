<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Button;
use App\Http\Requests\Button\StoreRequest;
use App\Http\Requests\Button\UpdateRequest;
use App\Models\Button as ButtonModel;
use Illuminate\Support\Collection;

class ButtonFactory
{

    public function buildFromStoreRequest(StoreRequest $request) : Button {
        return new Button(
            null,
            $request->organization_id ?? null,
            $request->text ?? null,
            $request->type ?? null,
            $request->callback_data ?? null,
            $request->json ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Button {
        return new Button(
            null,
            $request->organization_id ?? null,
            $request->text ?? null,
            $request->type ?? null,
            $request->callback_data ?? null,
            $request->json ?? null,
        );
    }

    public function buildFromModel(?ButtonModel $button) : ?Button {
        if (!$button){ return null; }

        return new Button(
            $button->id ?? null,
            $button->organization_id ?? null,
            $button->text ?? null,
            $button->type ?? null,
            $button->callback_data ?? null,
            $button->json ?? null,
            $button->created_at ?? null,
            $button->updated_at ?? null,
        );
    }

    /**
     * @param Collection|null $buttons
     * @return Button[]|null
     */
    public function buildFromModels(?Collection $buttons) : ?array {
        $domains = [];

        /** @var ButtonModel $button */
        foreach ($buttons as $button){
            $domains[] = $this->buildFromModel($button);
        }

        return $domains;
    }

    public function buildFromSaveFullButton(array $button, ?string $json, ?int $organization_id, ?int $id) : ?Button {
        /** @var array $callback_reply */
        $callback_reply = $button['callback_data'] ?? $button['reply'] ?? [];

        return new Button(
            $id,
            $organization_id ?? null,
            $button['text'] ?? "unnamed button",
            $button['type'] ?? "reply",
            json_encode($callback_reply) ?? null,
            $json ?? null,
        );
    }
}
