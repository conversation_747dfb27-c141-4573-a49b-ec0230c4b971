<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Interaction;
use App\Factories\Inventory\ClientFactory;
use App\Factories\UserFactory;
use App\Http\Requests\Interaction\StoreRequest;
use App\Http\Requests\Interaction\UpdateRequest;
use App\Models\Interaction as InteractionModel;
use Illuminate\Support\Collection;
use App\Factories\ChatBot\ConversationFactory;

class InteractionFactory
{
    public UserFactory $userFactory;
    public ClientFactory $clientFactory;
    public FlowFactory $flowFactory;
    public StepFactory $stepFactory;
    public ConversationFactory $conversationFactory;

    public function __construct(
        UserFactory $userFactory,
        ClientFactory $clientFactory,
        FlowFactory $flowFactory,
        StepFactory $stepFactory,
        ConversationFactory $conversationFactory
    ) {
        $this->userFactory = $userFactory;
        $this->clientFactory = $clientFactory;
        $this->flowFactory = $flowFactory;
        $this->stepFactory = $stepFactory;
        $this->conversationFactory = $conversationFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request): Interaction
    {
        return new Interaction(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->flow_id ?? null,
            $request->step_id ?? null,
            $request->conversation_id ?? null,
            $request->message ?? null,
            $request->answer ?? null,
            $request->result ?? null,
            $request->json ?? null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request): Interaction
    {
        return new Interaction(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->flow_id ?? null,
            $request->step_id ?? null,
            $request->conversation_id ?? null,
            $request->message ?? null,
            $request->answer ?? null,
            $request->result ?? null,
            $request->json ?? null
        );
    }

    public function buildFromModel(?InteractionModel $interaction, bool $with_relations = true): ?Interaction
    {
        if (!$interaction) {
            return null;
        }

        return new Interaction(
            $interaction->id ?? null,
            $interaction->organization_id ?? null,
            $interaction->user_id ?? null,
            $interaction->client_id ?? null,
            $interaction->flow_id ?? null,
            $interaction->step_id ?? null,
            $interaction->conversation_id ?? null,
            $interaction->message ?? null,
            $interaction->answer ?? null,
            $interaction->result ?? null,
            $interaction->json ?? null,
            $interaction->created_at ?? null,
            $interaction->updated_at ?? null,
            ($with_relations && $interaction->user_id) ? $this->userFactory->buildFromModel($interaction->user ?? null) : null,
            ($with_relations && $interaction->flow_id) ? $this->flowFactory->buildFromModel($interaction->flow ?? null, false) : null,
            ($with_relations && $interaction->step_id) ? $this->stepFactory->buildFromModel($interaction->step ?? null, false, false) : null,
            ($with_relations && $interaction->conversation_id) ? $this->conversationFactory->buildFromModel($interaction->conversation ?? null, false) : null
        );
    }

    /**
     * @param Collection|null $interactions
     * @param bool $with_relations
     * @return Interaction[]|null
     */
    public function buildFromModels(?Collection $interactions, bool $with_relations = false): ?array
    {
        if (!$interactions) {
            return null;
        }

        $domains = [];

        /** @var InteractionModel $interaction */
        foreach ($interactions as $interaction) {
            $domains[] = $this->buildFromModel($interaction, $with_relations);
        }

        return $domains;
    }
}
