<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Step;
use App\Domains\ChatBot\Template;
use App\Enums\ComponentFormat;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\Component\StoreRequest;
use App\Http\Requests\Component\UpdateRequest;
use App\Models\Component as ComponentModel;

class ComponentFactory
{

    public StepFactory $stepFactory;
    public ButtonFactory $buttonFactory;
    public TemplateFactory $templateFactory;
    private ?ParameterFactory $parameterFactory;
    private ClientFactory $clientFactory;

    public function __construct(ButtonFactory $buttonFactory, TemplateFactory $templateFactory) {
        $this->buttonFactory = $buttonFactory;
        $this->templateFactory = $templateFactory;
        $this->stepFactory = new StepFactory($this);
        $this->clientFactory = new ClientFactory();
    }

    private function ensureParameterFactory() : bool {
        if (!$this->parameterFactory){
            $this->parameterFactory = new ParameterFactory(
                new CampaignFactory(
                    $this->templateFactory,
                    new MessageFactory($this->clientFactory, $this->templateFactory),
                    $this->clientFactory,
                    new PhoneNumberFactory()
                )
            );
        }
        return true;
    }

    private function setFormat(StoreRequest|UpdateRequest $request): ComponentFormat {
        $format = ComponentFormat::TEXT;
        if ($request->has('format')) {
            $format = ComponentFormat::tryFrom($request->get('format'));
        }
        return $format;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Component {
        return new Component(
            null,
            $request->organization_id ?? null,
            $request->step_id ?? null,
            $request->template_id ?? null,
            $request->name ?? null,
            $request->type ?? null,
            $request->text ?? null,
            $this->setFormat($request) ?? null,
            $request->json ?? null,
            null,
            null,
            null,
            null,
            null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Component {
        return new Component(
            null,
            $request->organization_id ?? null,
            $request->step_id ?? null,
            $request->template_id ?? null,
            $request->name ?? null,
            $request->type ?? null,
            $request->text ?? null,
            $this->setFormat($request) ?? null,
            $request->json ?? null,
            null,
            null,
            null,
            null,
            null,
        );
    }

    public function buildFromModel(
        ?ComponentModel $component,
        bool $with_step = true,
        bool $with_buttons = true,
        bool $with_template = true,
        bool $with_parameters = true
    ) : ?Component {
        if (!$component){ return null; }

        if ($with_parameters && $this->ensureParameterFactory()) {
            $parameters = $this->parameterFactory->buildFromModels(
                $component->parameters ?? null, false, false
            );
        }

        return new Component(
            $component->id ?? null,
            $component->organization_id ?? null,
            $component->step_id ?? null,
            $component->template_id ?? null,
            $component->name ?? null,
            $component->type ?? null,
            $component->text ?? null,
            $component->format ?? null,
            $component->json ?? null,
            $component->created_at ?? null,
            $component->updated_at ?? null,
            ($with_step) ? $this->stepFactory->buildFromModel($component->step ?? null, false, false) : null,
            ($with_template) ? $this->templateFactory->buildFromModel($component->template ?? null) : null,
            ($with_buttons) ? $this->buttonFactory->buildFromModels($component->buttons ?? null) : null,
            $parameters ?? null
        );
    }

    /**
     * @param array $component
     * @param Step|null $step
     * @param string|null $json
     * @param int|null $id
     * @param Template|null $template
     * @return Component
     */
    public function buildFromSaveFullComponent(array $component, ?Step $step, ?string $json, ?int $id, ?Template $template = null) : Component {
        $text = $component['text'] ?? $component['body'] ?? $component['component'] ?? "";
        if (is_array($text)){
            $text = $text['text'] ?? $text['body'] ?? $text['component'] ?? "";
        }
        $format = ComponentFormat::tryFrom($component['format'] ?? "TEXT");

        return new Component(
            $id ?? null,
            $step?->organization_id ?? $template?->organization_id ?? null,
            $step?->id ?? null,
            $template?->id ?? null,
            $component['name'] ?? "unnamed component",
            $component['type'] ?? "message",
            $text ?? null,
            $format ?? null,
            $json ?? null,
            null,
            null,
            $step ?? null,
            $template ?? null,
            null,
        );
    }

    public function buildFromSaveFullTemplateComponent(
        array $component,
        Template $template,
        string $json,
        ?int $id = null
    ) : Component {
        $text = $component['text'] ?? null;
        $format = ComponentFormat::tryFrom($component['format'] ?? "TEXT");

        return new Component(
            $id ?? null,
            $template->organization_id ?? null,
            null, // step_id
            $template->id ?? null,
            $component['name'] ?? "unnamed component",
            $component['type'] ?? "body",
            $text ?? null,
            $format ?? ComponentFormat::TEXT,
            $json ?? null,
            null,
            null,
            null, // step
            $template ?? null,
            null, // buttons
            null  // parameters
        );
    }
}
