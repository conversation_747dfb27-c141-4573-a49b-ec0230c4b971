<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Parameter;
use App\Domains\ChatBot\Campaign;
use App\Domains\ChatBot\Component;
use App\Http\Requests\Parameter\StoreRequest;
use App\Http\Requests\Parameter\UpdateRequest;
use App\Models\Parameter as ParameterModel;

class ParameterFactory
{
    public CampaignFactory $campaignFactory;
    public ?ComponentFactory $componentFactory;

    public function __construct(CampaignFactory $campaignFactory) {
        $this->campaignFactory = $campaignFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Parameter {
        return new Parameter(
            null,
            $request->organization_id ?? null,
            $request->campaign_id ?? null,
            $request->component_id ?? null,
            $request->type ?? null,
            $request->value ?? null,
            $request->placeholder ?? null,
            null,
            null,
            null,
            null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Parameter {
        return new Parameter(
            null,
            $request->organization_id ?? null,
            $request->campaign_id ?? null,
            $request->component_id ?? null,
            $request->type ?? null,
            $request->value ?? null,
            $request->placeholder ?? null,
            null,
            null,
            null,
            null
        );
    }

    private function ensureComponentFactory() : bool {
        if(!$this->componentFactory){
            $this->componentFactory = new ComponentFactory(
                new ButtonFactory(),
                $this->campaignFactory->templateFactory,
            );
        }
        return true;
    }

    public function buildFromModel(?ParameterModel $parameter, bool $with_campaign = true, bool $with_component = true) : ?Parameter {
        if (!$parameter){ return null; }

        if($with_component && $this->ensureComponentFactory()) {
            $component =  $this->componentFactory->buildFromModel($parameter->component ?? null);
        }
        return new Parameter(
            $parameter->id ?? null,
            $parameter->organization_id ?? null,
            $parameter->campaign_id ?? null,
            $parameter->component_id ?? null,
            $parameter->type ?? null,
            $parameter->value ?? null,
            $parameter->placeholder ?? null,
            $parameter->created_at ?? null,
            $parameter->updated_at ?? null,
            ($with_campaign) ? $this->campaignFactory->buildFromModel($parameter->campaign ?? null) : null,
            $component ?? null,
        );
    }

    /**
     * @param ParameterModel[]|null $parameters
     * @return Parameter[]|null
     */
    public function buildFromModels(?array $parameters, bool $with_campaign = true, bool $with_component = true) : ?array {
        if (!$parameters){ return null; }

        $result = [];
        foreach ($parameters as $parameter) {
            $result[] = $this->buildFromModel($parameter, $with_campaign, $with_component);
        }
        return $result;
    }

    public function buildFromComponent(Component $component, $placeholder) : Parameter {
        return new Parameter(
            null,
            $component->organization_id ?? null,
            null,
            $component->id ?? null,
            'text',
            "undefined",
            $placeholder ?? null
        );
    }
}
