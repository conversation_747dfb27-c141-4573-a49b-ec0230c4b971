<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Conversation;
use App\Factories\Inventory\ClientFactory;
use App\Factories\UserFactory;
use App\Http\Requests\Conversation\StoreRequest;
use App\Http\Requests\Conversation\UpdateRequest;
use App\Models\Conversation as ConversationModel;
use Illuminate\Support\Collection;
use App\Factories\ChatBot\InteractionFactory;

class ConversationFactory
{
    public UserFactory $userFactory;
    public ClientFactory $clientFactory;
    public FlowFactory $flowFactory;
    public InteractionFactory $interactionFactory;

    public function __construct(
        UserFactory $userFactory,
        ClientFactory $clientFactory,
        FlowFactory $flowFactory,
        InteractionFactory $interactionFactory
    ) {
        $this->userFactory = $userFactory;
        $this->clientFactory = $clientFactory;
        $this->flowFactory = $flowFactory;
        $this->interactionFactory = $interactionFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request): Conversation
    {
        return new Conversation(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->flow_id ?? null,
            $request->json ?? null,
            $request->is_finished ?? false
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request): Conversation
    {
        return new Conversation(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->flow_id ?? null,
            $request->json ?? null,
            $request->is_finished ?? null
        );
    }

    public function buildFromModel(?ConversationModel $conversation, bool $with_relations = true): ?Conversation
    {
        if (!$conversation) {
            return null;
        }

        $interactions = null;
        if ($with_relations && $conversation->id) {
            $interactions = $this->interactionFactory->buildFromModels($conversation->interactions, false);
        }

        return new Conversation(
            $conversation->id ?? null,
            $conversation->organization_id ?? null,
            $conversation->user_id ?? null,
            $conversation->client_id ?? null,
            $conversation->flow_id ?? null,
            $conversation->json ?? null,
            $conversation->is_finished ?? false,
            $conversation->created_at ?? null,
            $conversation->updated_at ?? null,
            ($with_relations && $conversation->user_id) ? $this->userFactory->buildFromModel($conversation->user ?? null) : null,
            ($with_relations && $conversation->flow_id) ? $this->flowFactory->buildFromModel($conversation->flow ?? null, false) : null,
            $interactions
        );
    }

    /**
     * @param Collection|null $conversations
     * @param bool $with_relations
     * @return Conversation[]|null
     */
    public function buildFromModels(?Collection $conversations, bool $with_relations = false): ?array
    {
        if (!$conversations) {
            return null;
        }

        $domains = [];

        /** @var ConversationModel $conversation */
        foreach ($conversations as $conversation) {
            $domains[] = $this->buildFromModel($conversation, $with_relations);
        }

        return $domains;
    }
}
