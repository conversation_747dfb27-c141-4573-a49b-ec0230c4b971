<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\PhoneNumber;
use App\Http\Requests\PhoneNumber\StoreRequest;
use App\Http\Requests\PhoneNumber\UpdateRequest;
use App\Models\PhoneNumber as PhoneNumberModel;
use Illuminate\Support\Collection;

class PhoneNumberFactory
{
    public function buildFromStoreRequest(StoreRequest $request) : PhoneNumber {
        return new PhoneNumber(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->phone_number ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->is_active ?? true,
            $request->whatsapp_phone_number_id ?? null,
            $request->whatsapp_access_token ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : PhoneNumber {
        return new PhoneNumber(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->phone_number ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->is_active ?? null,
            $request->whatsapp_phone_number_id ?? null,
            $request->whatsapp_access_token ?? null,
        );
    }

    public function buildFromModel(?PhoneNumberModel $phoneNumber) : ?PhoneNumber {
        if (!$phoneNumber){ return null; }

        return new PhoneNumber(
            $phoneNumber->id ?? null,
            $phoneNumber->organization_id ?? null,
            $phoneNumber->user_id ?? null,
            $phoneNumber->client_id ?? null,
            $phoneNumber->phone_number ?? null,
            $phoneNumber->name ?? null,
            $phoneNumber->description ?? null,
            $phoneNumber->is_active ?? null,
            $phoneNumber->whatsapp_phone_number_id ?? null,
            $phoneNumber->whatsapp_access_token ?? null,
            $phoneNumber->created_at ?? null,
            $phoneNumber->updated_at ?? null,
            $phoneNumber->deleted_at ?? null,
            $phoneNumber->organization ?? null,
        );
    }

    /**
     * @param Collection|null $phoneNumbers
     * @return PhoneNumber[]|null
     */
    public function buildFromModels(?Collection $phoneNumbers) : ?array {
        if (!$phoneNumbers){ return null; }

        $domains = [];

        /** @var PhoneNumberModel $phoneNumber */
        foreach ($phoneNumbers as $phoneNumber){
            $domains[] = $this->buildFromModel($phoneNumber);
        }

        return $domains;
    }
}
