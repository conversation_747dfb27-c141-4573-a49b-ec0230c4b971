<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use App\Http\Requests\Step\StoreRequest;
use App\Http\Requests\Step\UpdateRequest;
use App\Models\Step as StepModel;
use Illuminate\Support\Collection;

class StepFactory
{
    public ComponentFactory $componentFactory;
    public FlowFactory $flowFactory;

    public function __construct(ComponentFactory $componentFactory)
    {
        $this->componentFactory = $componentFactory;
        $this->flowFactory = new FlowFactory($this);
    }

    public function buildFromStoreRequest(StoreRequest $request) : Step {
        return new Step(
            null,
            $request->organization_id ?? null,
            $request->flow_id ?? null,
            $request->step ?? null,
            $request->type ?? null,
            $request->position ?? null,
            $request->next_step ?? null,
            $request->earlier_step ?? null,
            $request->is_initial_step ?? null,
            $request->is_ending_step ?? null,
            $request->is_message ?? null,
            $request->is_interactive ?? null,
            $request->is_command ?? null,
            $request->is_input ?? false,
            $request->json ?? null,
            $request->input ?? null,
        );
    }

    public function buildFromSaveFullStep(array $step, Flow $flow, string $json, int $index, ?int $id) : Step {
        return new Step(
            $id ?? null,
            $flow->organization_id ?? null,
            $flow->id ?? null,
            $step['step'] ?? null,
            $step['type'] ?? null,
            $step['position'] ?? null,
            ((int) $step['position']) + 1,
            ((int) $step['position']) - 1,
            $index == 0,
            $index == ($flow->steps_count - 1),
            !empty($step['type']) && $step['type'] == 'message',
            !empty($step['type']) && ($step['type'] == 'interactive'),
            !empty($step['type']) && $step['type'] == 'command',
            !empty($step['type']) && $step['type'] == 'input',
            $json ?? null,
            $step['input'] ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Step {
        return new Step(
            null,
            $request->organization_id ?? null,
            $request->flow_id ?? null,
            $request->step ?? null,
            $request->type ?? null,
            $request->position ?? null,
            $request->next_step ?? null,
            $request->earlier_step ?? null,
            $request->is_initial_step ?? null,
            $request->is_ending_step ?? null,
            $request->is_message ?? null,
            $request->is_interactive ?? null,
            $request->is_command ?? null,
            $request->is_input ?? false,
            $request->json ?? null,
            $request->input ?? null
        );
    }

    public function buildFromModel(?StepModel $step, bool $with_flow = true, bool $with_component = true) : ?Step {
        if (!$step){ return null; }

        return new Step(
            $step->id ?? null,
            $step->organization_id ?? null,
            $step->flow_id ?? null,
            $step->step ?? null,
            $step->type ?? null,
            $step->position ?? null,
            $step->next_step ?? null,
            $step->earlier_step ?? null,
            $step->is_initial_step ?? null,
            $step->is_ending_step ?? null,
            $step->is_message ?? null,
            $step->is_interactive ?? null,
            $step->is_command ?? null,
            $step->is_input ?? false,
            $step->json ?? null,
            $step->input ?? null,
            $step->created_at ?? null,
            $step->updated_at ?? null,
            ($with_flow) ? $this->flowFactory->buildFromModel($step->flow ?? null, false) : null,
            ($with_component) ? $this->componentFactory->buildFromModel($step->component ?? null, false, true) : null,

        );
    }

    /**
     * @param Collection|null $steps
     * @param bool $with_flow
     * @param bool $with_component
     * @return Step[]|null
     */
    public function buildFromModels(?Collection $steps, bool $with_flow = false, bool $with_component = false) : ?array {
        $domains = [];

        /** @var StepModel $step */
        foreach ($steps as $step){
            $domains[] = $this->buildFromModel($step, $with_flow, $with_component);
        }

        return $domains;
    }
}
