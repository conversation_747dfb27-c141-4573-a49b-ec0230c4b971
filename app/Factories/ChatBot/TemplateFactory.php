<?php

namespace App\Factories\ChatBot;

use App\Http\Requests\Template\StoreRequest;
use App\Http\Requests\Template\UpdateRequest;
use App\Domains\ChatBot\Template;
use App\Models\Template as TemplateModel;

class TemplateFactory
{

    public ?ComponentFactory $componentFactory;
    public PhoneNumberFactory $phoneNumberFactory;

    public function __construct() {
        $this->phoneNumberFactory = new PhoneNumberFactory();
    }

    private function ensureComponentFactory() : bool {
        if (!isset($this->componentFactory)) {
            $this->componentFactory = new ComponentFactory(
                new ButtonFactory(),
                $this
            );
        }
        return true;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Template {
        return new Template(
            null,
            $request->organization_id ?? null,
            $request->phone_number_id ?? null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->name ?? null,
            $request->category ?? null,
            $request->parameter_format ?? null,
            $request->language ?? null,
            $request->library_template_name ?? null,
            $request->id_external ?? null,
            $request->status ?? null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Template {
        return new Template(
            null,
            $request->organization_id ?? null,
            $request->phone_number_id ?? null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->name ?? null,
            $request->category ?? null,
            $request->parameter_format ?? null,
            $request->language ?? null,
            $request->library_template_name ?? null,
            $request->id_external ?? null,
            $request->status ?? null
        );
    }

    public function buildFromModel(?TemplateModel $template, bool $with_components = true) : ?Template {
        if (!$template){ return null; }

        $isWhatsAppPublished = false;
        if ($template->whatsAppTemplate()->count()){
            $isWhatsAppPublished = true;
        }

        $components = null;
        if($with_components && $this->ensureComponentFactory()){
            $components = [];
            foreach ($template->components as $component){
                $components[] = $this->componentFactory->buildFromModel(
                    $component ?? null, false, true, false
                );
            }
        }

        $phoneNumber = null;
        if ($template->phoneNumber) {
            $phoneNumber = $this->phoneNumberFactory->buildFromModel($template->phoneNumber);
        }

        return new Template(
            $template->id ?? null,
            $template->organization_id ?? null,
            $template->phone_number_id ?? null,
            $template->user_id ?? null,
            $template->client_id ?? null,
            $template->name ?? null,
            $template->category ?? null,
            $template->parameter_format ?? null,
            $template->language ?? null,
            $template->library_template_name ?? null,
            $template->id_external ?? null,
            $template->status ?? null,
            $template->created_at ?? null,
            $template->updated_at ?? null,
            $isWhatsAppPublished ?? false,
            $components ?? null,
            $phoneNumber
        );
    }
}
