<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Campaign;
use App\Domains\ChatBot\Message;
use App\Domains\Inventory\Client;
use App\Enums\MessageStatus;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\Message\StoreRequest;
use App\Http\Requests\Message\UpdateRequest;
use App\Models\Message as MessageModel;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class MessageFactory
{

    public ClientFactory $clientFactory;
    public TemplateFactory $templateFactory;
    public CampaignFactory $campaignFactory;

    public function __construct(ClientFactory $clientFactory, TemplateFactory $templateFactory)
    {
        $this->clientFactory = $clientFactory;
        $this->templateFactory = $templateFactory;
        $this->campaignFactory = new CampaignFactory(
            $this->templateFactory,
            $this,
            $this->clientFactory,
            new PhoneNumberFactory()
        );
    }

    public function buildFromStoreRequest(StoreRequest $request): Message
    {
        return new Message(
            null,
            $request->organization_id ?? null,
            $request->campaign_id ?? null,
            $request->template_id ?? null,
            $request->client_id ?? null,
            $request->message ?? null,
            MessageStatus::tryFrom($request->status ?? 1) ?? MessageStatus::is_draft,
            $request->is_sent ?? false,
            $request->is_fail ?? false,
            $request->is_read ?? false,
            isset($request->sent_at) ? Carbon::parse($request->sent_at) : null,
            isset($request->scheduled_at) ? Carbon::parse($request->scheduled_at) : null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Message {
        return new Message(
            null,
            null,
            null,
            $request->template_id ?? null,
            null,
            $request->message ?? null,
            MessageStatus::tryFrom($request->status ?? 1) ?? MessageStatus::is_draft,
            $request->is_sent ?? false,
            $request->is_fail ?? false,
            $request->is_read ?? false,
            isset($request->sent_at) ? Carbon::parse($request->sent_at) : null,
            isset($request->scheduled_at) ? Carbon::parse($request->scheduled_at) : null,
        );
    }

    public function buildFromModel(?MessageModel $message, bool $with_relations = false): ?Message
    {
        if (!$message) {
            return null;
        }

        if($with_relations){
            $client = $this->clientFactory->buildFromModel($message->client ?? null);
            $template = $this->templateFactory->buildFromModel($message->template ?? null);
            $campaign = $this->campaignFactory->buildFromModel($message->campaign ?? null, false);
        }

        return new Message(
            $message->id ?? null,
            $message->organization_id ?? null,
            $message->campaign_id ?? null,
            $message->template_id ?? null,
            $message->client_id ?? null,
            $message->message ?? null,
            $message->status ?? MessageStatus::is_draft,
            $message->is_sent ?? false,
            $message->is_fail ?? false,
            $message->is_read ?? false,
            $message->sent_at ? Carbon::parse($message->sent_at) : null,
            $message->scheduled_at ? Carbon::parse($message->scheduled_at) : null,
            $message->created_at ? Carbon::parse($message->created_at) : null,
            $message->updated_at ? Carbon::parse($message->updated_at) : null,
            ($with_relations) ? $client : null,
            ($with_relations) ? $template : null,
            ($with_relations) ? $campaign : null,
        );
    }

    public function buildFromModels(?Collection $messages): ?array
    {
        if (!$messages) {  return null; }

        $domains = [];

        /** @var MessageModel $message */
        foreach ($messages as $message) {
            $domains[] = $this->buildFromModel($message);
        }

        return $domains;
    }

    public function buildFromGeneration(Campaign $campaign, Client $client, int $organization_id) : Message {
        return new Message(
            null,
            $organization_id,
            $campaign->id ?? null,
            $campaign->template_id ?? null,
            $client->id ?? null,
            $campaign->template->toWhatsAppRawMessage() ?? null,
            MessageStatus::is_sending,
            false,
            false,
            false,
            null,
            $campaign->scheduled_at ?? null,
            null,
            null,
            $client,
            $campaign->template,
            $campaign,
        );
    }
}
