<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Campaign;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\Campaign\StoreRequest;
use App\Http\Requests\Campaign\UpdateRequest;
use App\Models\Campaign as CampaignModel;
use Carbon\Carbon;

class CampaignFactory
{

    public TemplateFactory $templateFactory;
    public MessageFactory $messageFactory;
    public ClientFactory $clientFactory;
    private ParameterFactory $parameterFactory;
    private PhoneNumberFactory $phoneNumberFactory;

    public function __construct(
        TemplateFactory $templateFactory,
        MessageFactory $messageFactory,
        ClientFactory $clientFactory,
        PhoneNumberFactory $phoneNumberFactory
    ) {
        $this->templateFactory = $templateFactory;
        $this->messageFactory = $messageFactory;
        $this->clientFactory = $clientFactory;
        $this->phoneNumberFactory = $phoneNumberFactory;
        $this->parameterFactory = new ParameterFactory($this);
    }

    public function buildFromStoreRequest(StoreRequest $request): Campaign
    {
        return new Campaign(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->template_id ?? null,
            $request->phone_number_id ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->is_scheduled ?? false,
            $request->is_sent ?? false,
            $request->is_sending ?? false,
            $request->message_count ?? 0,
            isset($request->sent_at) ? Carbon::parse($request->sent_at) : null,
            isset($request->scheduled_at) ? Carbon::parse($request->scheduled_at) : null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Campaign {
        return new Campaign(
            null,
            null,
            null,
            $request->template_id ?? null,
            $request->phone_number_id ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->is_scheduled ?? false,
            $request->is_sent ?? false,
            $request->is_sending ?? false,
            $request->message_count ?? 0,
            isset($request->sent_at) ? Carbon::parse($request->sent_at) : null,
            isset($request->scheduled_at) ? Carbon::parse($request->scheduled_at) : null,
        );
    }

    public function buildFromModel(
        ?CampaignModel $campaign,
        $with_template = true,
        $with_messages = false,
        $with_clients = false,
        $with_parameters = false,
        $with_phone_number = false
    ): ?Campaign {
        if (!$campaign) {  return null; }

        if ($with_template) { $template = $this->templateFactory->buildFromModel($campaign->template ?? null); }
        if ($with_messages) { $messages = $this->messageFactory->buildFromModels($campaign->messages ?? null); }
        if ($with_clients) { $clients = $this->clientFactory->buildFromModels($campaign->clients ?? null); }
        if ($with_parameters) { $parameters = $this->parameterFactory->buildFromModels($campaign->parameters ?? null, false, false); }
        if ($with_phone_number) { $phoneNumber = $this->phoneNumberFactory->buildFromModel($campaign->phoneNumber ?? null); }

        return new Campaign(
            $campaign->id ?? null,
            $campaign->organization_id ?? null,
            $campaign->user_id ?? null,
            $campaign->template_id ?? null,
            $campaign->phone_number_id ?? null,
            $campaign->name ?? null,
            $campaign->description ?? null,
            $campaign->is_scheduled ?? false,
            $campaign->is_sent ?? false,
            $campaign->is_sending ?? false,
            $campaign->message_count ?? 0,
            $campaign->sent_at ? Carbon::parse($campaign->sent_at) : null,
            $campaign->scheduled_at ? Carbon::parse($campaign->scheduled_at) : null,
            $campaign->created_at ? Carbon::parse($campaign->created_at) : null,
            $campaign->updated_at ? Carbon::parse($campaign->updated_at) : null,
            $template ?? null,
            $phoneNumber ?? null,
            $messages ?? null,
            $clients ?? null,
            $parameters ?? null,
        );
    }
}
