<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Flow;
use App\Http\Requests\Flow\StoreRequest;
use App\Http\Requests\Flow\UpdateRequest;
use App\Models\Flow as FlowModel;

class FlowFactory
{

    public StepFactory $stepFactory;

    public function __construct(StepFactory $stepFactory) {
        $this->stepFactory = $stepFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Flow {
        return new Flow(
            null,
            $request->organization_id ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->steps_count ?? null,
            $request->json ?? null,
            $request->is_default_flow ?? false,
        );
    }

    public function buildFromSaveFullFlow(
        array $flow,
        string $json,
        ?int $steps_count,
        ?int $organization_id,
        ?int $id
    ) : Flow {
        return new Flow(
            $id ?? null,
            $organization_id ?? null,
            $flow ['name'] ?? null,
            $flow['description'] ?? null,
            $steps_count ?? null,
            $json ?? null,
            $flow['is_default_flow'] ?? false,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Flow {
        return new Flow(
            null,
            $request->organization_id ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->steps_count ?? null,
            $request->json ?? null,
            $request->is_default_flow ?? false,
        );
    }

    public function buildFromModel(?FlowModel $flow, bool $with_steps = true) : ?Flow {
        if (!$flow){ return null; }

        return new Flow(
            $flow->id ?? null,
            $flow->organization_id ?? null,
            $flow->name ?? null,
            $flow->description ?? null,
            $flow->steps_count ?? null,
            $flow->json ?? null,
            $flow->is_default_flow ?? false,
            $flow->created_at ?? null,
            $flow->updated_at ?? null,
            ($with_steps) ? $this->stepFactory->buildFromModels($flow->steps ?? null, false, true) : null
        );
    }
}
