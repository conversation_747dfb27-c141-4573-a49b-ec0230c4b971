<?php

namespace App\Factories;

use App\Http\Requests\Auth\RegisterRequest;
use App\Domains\User;
use App\Http\Requests\User\StoreRequest;
use App\Http\Requests\User\UpdateRequest;
use App\Models\User as UserModel;

class UserFactory
{
    public OrganizationFactory $organizationFactory;

    public function __construct(OrganizationFactory $organizationFactory){
        $this->organizationFactory = $organizationFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : User {
        return new User(
            null,
            $request->profile_id ?? null,
            null,
            $request->first_name ?? "",
            $request->last_name ?? "",
            $request->username ?? "",
            $request->email ?? "",
            $request->password ?? null,
            $request->cpf ?? null,
            $request->phone ?? null,
        );
    }


    public function buildFromUpdateRequest(UpdateRequest $request) : User {
        return new User(
            null,
            $request->profile_id ?? null,
            null,
            $request->first_name ?? "",
            $request->last_name ?? "",
            $request->username ?? "",
            $request->email ?? "",
            $request->password ?? null,
            $request->cpf ?? null,
            $request->phone ?? null,
        );
    }

    public function buildFromRegisterRequest(RegisterRequest $request) : User {
        return new User(
            null,
            $request->profile_id ?? null,
            $request->organization_id ?? null,
            $request->first_name ?? "",
            $request->last_name ?? "",
            $request->username ?? "",
            $request->email ?? "",
            $request->password ?? null,
            $request->cpf ?? null,
            $request->phone ?? null,
            null,
            $this->organizationFactory->buildFromStoreArray($request->organization ?? null)
        );
    }

    public function buildFromModel(?UserModel $user) : ?User {
        if(!$user){ return null; }

        return new User(
            $user->id ?? null,
            $user->profile_id ?? null,
            $user->organization_id ?? null,
            $user->first_name ?? "",
            $user->last_name ?? "",
            $user->username ?? "",
            $user->email ?? "",
            null,
            $user->cpf ?? null,
            $user->phone ?? null,
            null,
            $this->organizationFactory->buildFromModel($user->organization()->first() ?? null),
        );
    }
}
