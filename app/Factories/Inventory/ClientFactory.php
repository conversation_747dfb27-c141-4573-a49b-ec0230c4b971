<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Client;
use App\Http\Requests\Client\StoreRequest;
use App\Http\Requests\Client\UpdateRequest;
use App\Models\Client as ClientModel;
use Illuminate\Support\Collection;

class ClientFactory
{

    public function buildFromStoreRequest(StoreRequest $request) : Client {
        return new Client(
            null,
            $request->organization_id ?? null,
            $request->name ?? null,
            $request->phone ?? null,
            $request->email ?? null,
            $request->profession ?? null,
            $request->birthdate ?? null,
            $request->cpf ?? null,
            $request->cnpj ?? null,
            $request->service ?? null,
            $request->address ?? null,
            $request->number ?? null,
            $request->neighborhood ?? null,
            $request->cep ?? null,
            $request->complement ?? null,
            $request->civil_state ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Client {
        return new Client(
            null,
            null,
            $request->name ?? null,
            $request->phone ?? null,
            $request->email ?? null,
            $request->profession ?? null,
            $request->birthdate ?? null,
            $request->cpf ?? null,
            $request->cnpj ?? null,
            $request->service ?? null,
            $request->address ?? null,
            $request->number ?? null,
            $request->neighborhood ?? null,
            $request->cep ?? null,
            $request->complement ?? null,
            $request->civil_state ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(?ClientModel $client) : ?Client {
        if(!$client){
            return null;
        }

        return new Client(
            $client->id ?? null,
            $client->organization_id ?? null,
            $client->name ?? null,
            $client->phone ?? null,
            $client->email ?? null,
            $client->profession ?? null,
            $client->birthdate ?? null,
            $client->cpf ?? null,
            $client->cnpj ?? null,
            $client->service ?? null,
            $client->address ?? null,
            $client->number ?? null,
            $client->neighborhood ?? null,
            $client->cep ?? null,
            $client->complement ?? null,
            $client->civil_state ?? null,
            $client->description ?? null,
            $client->created_at ?? null,
            $client->updated_at ?? null,
        );
    }

    public function buildFromModels(?Collection $clients) : ?array {
        if(!$clients){ return []; }

        $clientsArray = [];
        foreach ($clients as $client){
            $clientsArray[] = $this->buildFromModel($client);
        }
        return $clientsArray;
    }
}
