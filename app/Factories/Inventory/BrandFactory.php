<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Brand;
use App\Http\Requests\Brand\StoreRequest;
use App\Http\Requests\Brand\UpdateRequest;
use App\Models\Brand as BrandModel;

class BrandFactory
{

    public function buildFromStoreRequest(StoreRequest $request) : Brand {
        return new Brand(
            null,
            $request->organization_id ?? null,
            $request->name ?? "",
            $request->description ?? "",
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Brand {
        return new Brand(
            null,
            $request->organization_id ?? null,
            $request->name ?? "",
            $request->description ?? "",
        );
    }

    public function buildFromModel(?BrandModel $brand) : ?Brand {
        if (!$brand){ return null; }

        return new Brand(
            $brand->id ?? null,
            $brand->organization_id ?? null,
            $brand->name ?? null,
            $brand->description ?? "",
            $brand->created_at ?? "",
            $brand->updated_at ?? "",
        );
    }
}
