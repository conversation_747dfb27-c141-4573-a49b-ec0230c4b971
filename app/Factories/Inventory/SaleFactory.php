<?php

namespace App\Factories\Inventory;

use App\Domains\User;
use App\Domains\Inventory\Shop;
use App\Domains\Inventory\Sale;
use App\Factories\UserFactory;
use App\Http\Requests\Sale\StoreRequest;
use App\Http\Requests\Sale\UpdateRequest;
use App\Models\User as UserModel;
use App\Models\Shop as ShopModel;
use App\Models\Sale as SaleModel;
use Illuminate\Http\Request;

class SaleFactory
{

    public UserFactory $userFactory;
    public ShopFactory $shopFactory;
    public ClientFactory $clientFactory;
    public ItemFactory $itemFactory;

    public function __construct(
        UserFactory $userFactory,
        ShopFactory $shopFactory,
        ClientFactory $clientFactory
    ) {
        $this->userFactory = $userFactory;
        $this->shopFactory = $shopFactory;
        $this->clientFactory = $clientFactory;
        $this->itemFactory = new ItemFactory(
            $this, new ProductFactory(
                new BrandFactory()
            )
        );
    }

    public function buildFromStoreRequest(StoreRequest $request) : Sale {
        return new Sale(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->shop_id ?? null,
            $request->client_id ?? null,
            $request->total_value ?? null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Sale {
        return new Sale(
            null,
            null,
            $request->user_id ?? null,
            $request->shop_id ?? null,
            $request->client_id ?? null,
            $request->total_value ?? null
        );
    }

    public function buildFromModel(
        ?SaleModel $sale,
        bool $with_user = true,
        bool $with_shop = true,
        bool $with_client = true,
        bool $with_items = true,
    ) : ?Sale {
        if(!$sale){ return null; }

        $user = ($with_user) ? $this->userFactory->buildFromModel($sale->user) : null;
        $shop = ($with_shop) ? $this->shopFactory->buildFromModel($sale->shop) : null;
        $client = ($with_client) ? $this->clientFactory->buildFromModel($sale->client) : null;
        $items = ($with_items) ? $this->itemFactory->buildFromSale($sale) : null;

        return new Sale(
            $sale->id ?? null,
            $sale->organization_id ?? null,
            $sale->user_id ?? null,
            $sale->shop_id ?? null,
            $sale->client_id ?? null,
            $sale->total_value ?? null,
            $sale->created_at ?? null,
            $sale->updated_at ?? null,
            $user ?? null,
            $shop ?? null,
            $client ?? null,
            $items ?? null
        );
    }
}
