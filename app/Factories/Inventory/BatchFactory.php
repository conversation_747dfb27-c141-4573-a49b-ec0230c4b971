<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Batch;
use App\Http\Requests\Batch\StoreRequest;
use App\Http\Requests\Batch\UpdateRequest;
use App\Models\Batch as BatchModel;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class BatchFactory
{

    public ProductFactory $productFactory;
    public ShopFactory $shopFactory;

    public function __construct(ProductFactory $productFactory, ShopFactory $shopFactory)
    {
        $this->productFactory = $productFactory;
        $this->shopFactory = $shopFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Batch {
        return new Batch(
            null,
            $request->organization_id ?? null,
            $request->shop_id ?? null,
            $request->product_id ?? null,
            $request->batch_number ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->quantity ?? null,
            $this->datedAt($request->produced_at ?? null) ?? null,
            $this->datedAt($request->expired_at ?? null) ?? null,
            $this->datedAt($request->processed_at ?? null) ?? null,
            $request->is_processed_at_stock ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Batch {
        return new Batch(
            null,
            $request->organization_id ?? null,
            $request->shop_id ?? null,
            $request->product_id ?? null,
            $request->batch_number ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->quantity ?? null,
            $this->datedAt($request->produced_at ?? null) ?? null,
            $this->datedAt($request->expired_at ?? null) ?? null,
            $this->datedAt($request->processed_at ?? null) ?? null,
            $request->is_processed_at_stock ?? null,
        );
    }

    public function buildFromModel(?BatchModel $batch, bool $with_product = true, bool $with_shop = true) : ?Batch {
        if (!$batch){ return null; }

        if($with_product){ $product = $this->productFactory->buildFromModel($batch->product ?? null); }
        if($with_shop){ $shop = $this->shopFactory->buildFromModel($batch->shop ?? null); }

        return new Batch(
            $batch->id ?? null,
            $batch->organization_id ?? null,
            $batch->shop_id ?? null,
            $batch->product_id ?? null,
            $batch->batch_number ?? null,
            $batch->name ?? null,
            $batch->description ?? null,
            $batch->quantity ?? null,
            $this->datedAt($batch->produced_at ?? null) ?? null,
            $this->datedAt($batch->expired_at ?? null) ?? null,
            $this->datedAt($batch->processed_at ?? null) ?? null,
            $batch->is_processed_at_stock ?? null,
            $batch->created_at ?? null,
            $batch->updated_at ?? null,
            $product ?? null,
            $shop ?? null
        );
    }

    private function datedAt(null|string|Carbon $date) : ?Carbon {
        if(!$date){ return null; }
        if($date instanceof Carbon){ return $date; }
        try{
            return Carbon::parse($date);
        } catch(\Throwable $e1) {
            Log::info($e1->getMessage());
            try{
                return Carbon::createFromFormat('d/m/Y', $date);
            }catch(\Throwable $e2) {
                Log::info($e2->getMessage());
                return null;
            }
        }
    }
}
