<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\DepartmentUser;
use App\Factories\UserFactory;
use App\Http\Requests\DepartmentUser\StoreRequest;
use App\Http\Requests\DepartmentUser\UpdateRequest;
use App\Models\DepartmentUser as DepartmentUserModel;

class DepartmentUserFactory
{

    public UserFactory $userFactory;
    public DepartmentFactory $departmentFactory;

    public function __construct(
        UserFactory $userFactory,
        DepartmentFactory $departmentFactory,
    ) {
        $this->userFactory = $userFactory;
        $this->departmentFactory = $departmentFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : DepartmentUser {
        return new DepartmentUser(
            null,
            $request->user_id ?? null,
            $request->department_id ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : DepartmentUser {
        return new DepartmentUser(
            null,
            $request->user_id ?? null,
            $request->department_id ?? null,
        );
    }

    public function buildFromModel(DepartmentUserModel $departmentUser) : DepartmentUser {
        return new DepartmentUser(
            $departmentUser->id ?? null,
            $departmentUser->user_id ?? null,
            $departmentUser->department_id ?? null,
            $departmentUser->created_at ?? null,
            $departmentUser->updated_at ?? null,
            $this->userFactory->buildFromModel($departmentUser->user()->first() ?? null) ?? null,
            $this->departmentFactory->buildFromModel($departmentUser->department()->first() ?? null) ?? null
        );
    }
}
