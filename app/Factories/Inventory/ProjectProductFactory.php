<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\ProjectProduct;
use App\Http\Requests\ProjectProduct\StoreRequest;
use App\Http\Requests\ProjectProduct\UpdateRequest;
use App\Models\Project;
use App\Models\ProjectProduct as ProjectProductModel;

class ProjectProductFactory
{

    public ProjectFactory $projectFactory;
    public ProductFactory $productFactory;

    public function __construct(
        ProjectFactory $projectFactory,
        ProductFactory $productFactory,
    ) {
        $this->projectFactory = $projectFactory;
        $this->productFactory = $productFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : ProjectProduct {
        return new ProjectProduct(
            null,
            $request->project_id ?? null,
            $request->product_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : ProjectProduct {
        return new ProjectProduct(
            null,
            $request->project_id ?? null,
            $request->product_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(
        ProjectProductModel $projectProduct,
        $with_project = true,
        $with_product = true
    ) : ProjectProduct {

        $project = ($with_project) ? $this->projectFactory->buildFromModel($projectProduct->project ?? null) : null;
        $product = ($with_product) ? $this->productFactory->buildFromModel($projectProduct->product ?? null) : null;

        return new ProjectProduct(
            $projectProduct->id ?? null,
            $projectProduct->project_id ?? null,
            $projectProduct->product_id ?? null,
            $projectProduct->quantity ?? null,
            $projectProduct->value ?? null,
            $projectProduct->description ?? null,
            $projectProduct->created_at ?? null,
            $projectProduct->updated_at ?? null,
            $project ?? null,
            $product ?? null
        );
    }

    public function buildFromProject(Project $project) : array {
        $products = [];
        foreach ($project->products as $product){
            $products[] = new ProjectProduct(
                $product->pivot->id ?? null,
                $project->id ?? null,
                $product->id ?? null,
                $product->pivot->quantity ?? null,
                $product->pivot->value ?? null,
                $product->pivot->description ?? null,
                $projectProduct->created_at ?? null,
                $projectProduct->updated_at ?? null,
                null,
                $this->productFactory->buildFromModel($product ?? null) ?? null,
            );
        }
        return $products;
    }
}
