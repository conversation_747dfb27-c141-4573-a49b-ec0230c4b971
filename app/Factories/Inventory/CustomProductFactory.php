<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\CustomProduct;
use App\Http\Requests\CustomProduct\StoreRequest;
use App\Http\Requests\CustomProduct\UpdateRequest;
use App\Models\Budget;
use App\Models\CustomProduct as ProductModel;
use App\Models\Project;
use Illuminate\Support\Collection;

class CustomProductFactory
{
    public BudgetFactory $budgetFactory;
    public ProjectFactory $projectFactory;
    public function __construct(
        BudgetFactory $budgetFactory,
        ProjectFactory $projectFactory
    ){
        $this->budgetFactory = $budgetFactory;
        $this->projectFactory = $projectFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : CustomProduct {
        return new CustomProduct(
            null,
            $request->project_id ?? null,
            $request->budget_id ?? null,
            $request->quantity ?? "",
            $request->value ?? "",
            $request->description ?? ""
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : CustomProduct {
        return new CustomProduct(
            null,
            $request->project_id ?? null,
            $request->budget_id ?? null,
            $request->quantity ?? "",
            $request->value ?? "",
            $request->description ?? ""
        );
    }

    public function buildFromModel(
        ?ProductModel $product,
        bool $with_project = true,
        bool $with_budget = true
    ) : ?CustomProduct {
        if(!$product) { return null; }

        $project = ($with_project) ? $this->projectFactory->buildFromModel($product->project ?? null) : null;
        $budget = ($with_budget) ? $this->budgetFactory->buildFromModel($product->budget ?? null) : null;

        return new CustomProduct(
            $product->id ?? null,
            $product->project_id ?? null,
            $product->budget_id ?? null,
            $product->quantity ?? "",
            $product->value ?? "",
            $product->description ?? null,
            $product->created_at ?? null,
            $product->updated_at ?? null,
            $project ?? null,
            $budget ?? null
        );
    }

    public function buildFromProject(Project $project) : array {
        $products = [];
        foreach ($project->custom_products as $product){
            $products[] = $this->buildFromRow($product);
        }
        return $products;
    }

    public function buildFromBudget(Budget $budget) : array {
        $products = [];
        foreach ($budget->custom_products as $product){
            $products[] = $this->buildFromRow($product);
        }
        return $products;
    }

    private function buildFromRow(mixed $product) : CustomProduct
    {
        return new CustomProduct(
            $product->id ?? null,
            $product->project_id ?? null,
            $product->budget_id ?? null,
            $product->quantity ?? "",
            $product->value ?? "",
            $product->description ?? null,
            $product->created_at ?? null,
            $product->updated_at ?? null,
        );
    }

    public function buildFromModelArray(Collection $productModels) : array {
        $products = [];
        foreach ($productModels as $product){
            $products[] = $this->buildFromModel($product);
        }
        return $products;
    }
}
