<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\ProjectProduct;
use App\Domains\Inventory\Shop;
use App\Domains\Inventory\StockExit;
use App\Factories\UserFactory;
use App\Http\Requests\StockExit\StoreRequest;
use App\Http\Requests\StockExit\UpdateRequest;
use App\Models\StockExit as StockExitModel;
use App\Repositories\ProductRepository;

class StockExitFactory
{

    public UserFactory $userFactory;
    public BrandFactory $brandFactory;
    public ProductFactory $productFactory;
    public ClientFactory $clientFactory;
    public ProjectFactory $projectFactory;
    public ProductRepository $productRepository;
    public BatchFactory $batchFactory;
    public ShopFactory $shopFactory;

    public function __construct(
        UserFactory $userFactory,
        BrandFactory $brandFactory,
        ProductFactory $productFactory,
        ClientFactory $clientFactory,
        ProjectFactory $projectFactory,
        ProductRepository $productRepository,
        BatchFactory $batchFactory,
        ShopFactory $shopFactory
    ) {
        $this->userFactory = $userFactory;
        $this->brandFactory = $brandFactory;
        $this->productFactory = $productFactory;
        $this->clientFactory = $clientFactory;
        $this->projectFactory = $projectFactory;
        $this->productRepository = $productRepository;
        $this->batchFactory = $batchFactory;
        $this->shopFactory = $shopFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : StockExit {
        return new StockExit(
            null,
            $request->organization_id ?? null,
            $request->shop_id ?? null,
            $request->user()->id ?? null,
            $request->brand_id ?? null,
            $request->product_id ?? null,
            $request->batch_id ?? null,
            $request->client_id ?? null,
            $request->project_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
            $request->description ?? null,
            null,
            null,
            null,
            $this->productRepository->fetchById($request->product_id ?? null) ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : StockExit {
        return new StockExit(
            null,
            null,
            null,
            null,
            $request->brand_id ?? null,
            $request->product_id ?? null,
            $request->batch_id ?? null,
            $request->client_id ?? null,
            $request->project_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(
        StockExitModel $stockExit, bool $with_batch = true, bool $with_shop = true
    ) : StockExit {

        if($with_batch){ $batch = $this->batchFactory->buildFromModel($stockExit->batch ?? null, false, false); }
        if($with_shop){ $shop = $this->shopFactory->buildFromModel($stockExit->shop ?? null); }

        return new StockExit(
            $stockExit->id ?? null,
            $stockExit->organization_id ?? null,
            $stockExit->user_id ?? null,
            $stockExit->shop_id ?? null,
            $stockExit->brand_id ?? null,
            $stockExit->product_id ?? null,
            $stockExit->batch_id ?? null,
            $stockExit->client_id ?? null,
            $stockExit->project_id ?? null,
            $stockExit->quantity ?? null,
            $stockExit->value ?? null,
            $stockExit->description ?? null,
            $stockExit->created_at ?? null,
            $stockExit->updated_at ?? null,
            $this->userFactory->buildFromModel($stockExit->user ?? null) ?? null,
            $this->productFactory->buildFromModel($stockExit->product ?? null) ?? null,
            $this->projectFactory->buildFromModel($stockExit->project ?? null) ?? null,
            $batch ?? null,
            $shop ?? null
        );
    }

    public function buildFromProjectProduct(ProjectProduct $projectProduct, ?Shop $shop = null) : StockExit {
        $user = request()->user();

        return new StockExit(
            null,
            $user->organization_id ?? null,
            ($shop?->id) ?? null,
            $user->id ?? null,
            $projectProduct->product->brand_id ?? null,
            $projectProduct->product_id ?? null,
            $projectProduct->project->client_id ?? null,
            $projectProduct->project_id ?? null,
            $projectProduct->quantity ?? null,
            $projectProduct->value ?? null,
            $projectProduct->description ?? null,
            null,
            null,
            null,
            null,
            $projectProduct->product ?? null,
            $projectProduct->project ?? null,
            null,
            $shop ?? null,
        );
    }
}
