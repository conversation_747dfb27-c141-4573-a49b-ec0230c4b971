<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Batch;
use App\Domains\Inventory\StockEntry;
use App\Factories\UserFactory;
use App\Http\Requests\StockEntry\StoreRequest;
use App\Http\Requests\StockEntry\UpdateRequest;
use App\Models\StockEntry as StockEntryModel;
use App\Repositories\ProductRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class StockEntryFactory
{

    public UserFactory $userFactory;
    public BrandFactory $brandFactory;
    public ProductFactory $productFactory;
    public ClientFactory $clientFactory;
    public ProjectFactory $projectFactory;
    public ProductRepository $productRepository;
    public BatchFactory $batchFactory;
    public ShopFactory $shopFactory;

    public function __construct(
        UserFactory $userFactory,
        BrandFactory $brandFactory,
        ProductFactory $productFactory,
        ClientFactory $clientFactory,
        ProjectFactory $projectFactory,
        ProductRepository $productRepository,
        BatchFactory $batchFactory,
        ShopFactory $shopFactory
    ) {
        $this->userFactory = $userFactory;
        $this->brandFactory = $brandFactory;
        $this->productFactory = $productFactory;
        $this->clientFactory = $clientFactory;
        $this->projectFactory = $projectFactory;
        $this->productRepository = $productRepository;
        $this->batchFactory = $batchFactory;
        $this->shopFactory = $shopFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : StockEntry {
        return new StockEntry(
            null,
            $request->organization_id ?? null,
            $request->shop_id ?? null,
            $request->user()->id ?? null,
            $request->brand_id ?? null,
            $request->product_id ?? null,
            $request->batch_id ?? null,
            $request->client_id ?? null,
            $request->project_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
            $request->description ?? null,
            null,
            null,
            null,
            $this->productRepository->fetchById($request->product_id ?? null) ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : StockEntry {
        return new StockEntry(
            null,
            null,
            null,
            null,
            $request->brand_id ?? null,
            $request->product_id ?? null,
            $request->batch_id ?? null,
            $request->client_id ?? null,
            $request->project_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(StockEntryModel $stockEntry, bool $with_batch = true, bool $with_shop = true) : StockEntry {
        if($with_batch){ $batch = $this->batchFactory->buildFromModel($stockExit->batch ?? null, false, false); }
        if($with_shop){ $shop = $this->shopFactory->buildFromModel($stockExit->shop ?? null); }

        return new StockEntry(
            $stockEntry->id ?? null,
            $stockEntry->organization_id ?? null,
            $stockEntry->shop_id ?? null,
            $stockEntry->user_id ?? null,
            $stockEntry->brand_id ?? null,
            $stockEntry->product_id ?? null,
            $stockEntry->batch_id ?? null,
            $stockEntry->client_id ?? null,
            $stockEntry->project_id ?? null,
            $stockEntry->quantity ?? null,
            $stockEntry->value ?? null,
            $stockEntry->description ?? null,
            $stockEntry->created_at ?? null,
            $stockEntry->updated_at ?? null,
            $this->userFactory->buildFromModel($stockEntry->user ?? null) ?? null,
            $this->productFactory->buildFromModel($stockEntry->product ?? null) ?? null,
            $this->projectFactory->buildFromModel($stockEntry->project ?? null, false, false, false, false) ?? null,
            $batch ?? null,
            $shop ?? null
        );
    }

    public function buildFromBatch(Batch $batch, int $user_id) : StockEntry {
        return new StockEntry(
            null,
            $batch->organization_id ?? null,
            $batch->shop_id ?? null,
            $user_id ?? null,
            $batch->product->brand_id ?? null,
            $batch->product->id ?? null,
            $batch->id ?? null,
            null,
            null,
            $batch->quantity ?? null,
            null,
            $batch->description ?? null,
            Carbon::now()?? null,
            Carbon::now() ?? null,
            null,
            $batch->product ?? null,
            null,
            $batch ?? null,
            $batch->shop ?? null
        );
    }
}
