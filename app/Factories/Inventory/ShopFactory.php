<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Shop;
use App\Http\Requests\Shop\StoreRequest;
use App\Http\Requests\Shop\UpdateRequest;
use App\Models\Shop as ShopModel;

class ShopFactory
{

    public function buildFromStoreRequest(StoreRequest $request) : Shop {
        return new Shop(
            null,
            $request->organization_id ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->is_active ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Shop {
        return new Shop(
            null,
            null,
            $request->name ?? null,
            $request->description ?? null,
            $request->is_active ?? null,
        );
    }

    public function buildFromModel(?ShopModel $shop) : ?Shop {
        if (!$shop) { return null; }

        return new Shop(
            $shop->id ?? null,
            $shop->organization_id ?? null,
            $shop->name ?? null,
            $shop->description ?? null,
            $shop->is_active ?? null,
            $shop->created_at ?? null,
            $shop->updated_at ?? null,
        );
    }
}
