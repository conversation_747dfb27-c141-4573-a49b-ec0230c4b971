<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Budget;
use App\Http\Requests\Budget\StoreRequest;
use App\Http\Requests\Budget\UpdateRequest;
use App\Models\Budget as BudgetModel;

class BudgetFactory
{

    public ClientFactory $clientFactory;
    public ProductFactory $productFactory;
    public ProjectFactory $projectFactory;
    public BudgetProductFactory $budgetProductFactory;
    public CustomProductFactory $customProductFactory;

    public function __construct(ClientFactory $clientFactory, ProductFactory $productFactory) {
        $this->clientFactory = $clientFactory;
        $this->productFactory = $productFactory;
        $this->projectFactory = new ProjectFactory($clientFactory, $productFactory, $this);
        $this->budgetProductFactory = new BudgetProductFactory($this, $this->productFactory);
        $this->customProductFactory = new CustomProductFactory($this, $this->projectFactory);
    }

    public function buildFromStoreRequest(StoreRequest $request) : Budget {
        return new Budget(
            null,
            $request->organization_id ?? null,
            $request->client_id ?? null,
            $request->value ?? null,
            $request->cost ?? null,
            $request->name ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Budget {
        return new Budget(
            null,
            null,
            $request->client_id ?? null,
            $request->value ?? null,
            $request->cost ?? null,
            $request->name ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(
        ?BudgetModel $budget,
        bool $with_products = true,
        bool $with_custom_products = true,
        bool $with_projects = true,
    ) : ?Budget {
        if(!$budget){ return null; }

        $products = ($with_products) ? $this->budgetProductFactory->buildFromBudget($budget) : null;
        $custom_products = ($with_custom_products) ? $this->customProductFactory->buildFromBudget($budget) : null;
        $projects = ($with_projects) ? $this->projectFactory->buildArrayFromBudget($budget) : null;

        return new Budget(
            $budget->id ?? null,
            $budget->organization_id ?? null,
            $budget->client_id ?? null,
            $budget->value ?? null,
            $budget->cost ?? null,
            $budget->name ?? null,
            $budget->description ?? null,
            $budget->created_at ?? null,
            $budget->updated_at ?? null,
            $this->clientFactory->buildFromModel($budget->client) ?? null,
            $products ?? null,
            $custom_products ?? null,
            $projects ?? null,
        );
    }
}
