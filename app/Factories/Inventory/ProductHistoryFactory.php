<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Product;
use App\Domains\Inventory\ProductHistory;
use App\Factories\UserFactory;
use App\Http\Requests\ProductHistory\StoreRequest;
use App\Http\Requests\ProductHistory\UpdateRequest;
use App\Models\ProductHistory as ProductHistoryModel;

class ProductHistoryFactory
{

    public UserFactory $userFactory;
    public ProductFactory $productFactory;

    public const PRICE_ALIAS = "Preço";
    public const PRICE_FIELD = "price";

    public function __construct(
        UserFactory $userFactory,
        ProductFactory $productFactory,
    ) {
        $this->userFactory = $userFactory;
        $this->productFactory = $productFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : ProductHistory {
        return new ProductHistory(
            null,
            $request->user()->id ?? null,
            $request->product_id ?? null,
            $request->field ?? null,
            $request->alias ?? null,
            $request->old ?? null,
            $request->new ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : ProductHistory {
        return new ProductHistory(
            null,
            $request->user()->id ?? null,
            $request->product_id ?? null,
            $request->field ?? null,
            $request->alias ?? null,
            $request->old ?? null,
            $request->new ?? null,
        );
    }

    public function buildFromModel(ProductHistoryModel $productHistory) : ProductHistory {
        return new ProductHistory(
            $productHistory->id ?? null,
            $productHistory->user_id ?? null,
            $productHistory->product_id ?? null,
            $productHistory->field ?? null,
            $productHistory->alias ?? null,
            $productHistory->old ?? null,
            $productHistory->new ?? null,
            $productHistory->created_at ?? null,
            $productHistory->updated_at ?? null,
            $this->userFactory->buildFromModel($productHistory->user()->first() ?? null) ?? null,
            $this->productFactory->buildFromModel($productHistory->product()->first() ?? null) ?? null,
        );
    }

    public function buildFromProducts(Product $product, Product $old_product) : ProductHistory {
        return new ProductHistory(
            null,
            request()->user()->id ?? null,
            $product->id ?? null,
            self::PRICE_FIELD,
            self::PRICE_ALIAS,
            $old_product->price ?? 0.0,
            $product->price ?? 0.0,
        );
    }
}
