<?php

namespace App\Factories\Report;

use App\Domains\Inventory\Report\StockExitRow;
use App\Models\StockExit;

class StockExitRowFactory
{

    public function buildFromStockExit(StockExit $stockExit) : StockExitRow {
        return new StockExitRow(
            ($stockExit->user->first_name . " " . $stockExit->user->last_name) ?? null,
            $stockExit->product->name ?? null,
            $stockExit->client->name ?? null,
            $stockExit->project->name ?? null,
            $stockExit->quantity ?? null,
            $stockExit->value ?? null,
            $stockExit->created_at->format("d/m/Y H:i:s") ?? null
        );
    }
}
