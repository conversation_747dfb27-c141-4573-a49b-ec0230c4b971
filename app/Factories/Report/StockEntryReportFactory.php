<?php

namespace App\Factories\Report;

use App\Domains\Inventory\Report\StockEntryReport;
use Illuminate\Support\Collection;

class StockEntryReportFactory
{

    private StockEntryGroupFactory $stockEntryGroupFactory;

    public function __construct(
        StockEntryGroupFactory $stockEntryGroupFactory,
    ){
        $this->stockEntryGroupFactory = $stockEntryGroupFactory;
    }

    public function buildFromModels(Collection $entries, ?string $grouped_by) : StockEntryReport {
        $total_quantity = 0;
        $total_value = 0.0;
        $groups = $this->stockEntryGroupFactory->buildFromModels($entries, $grouped_by);
        foreach ($groups as $group){
            $total_quantity += $group->total_quantity;
            $total_value += $group->total_value;
        }
        return new StockEntryReport(
            $total_quantity ?? 0,
            $total_value ?? 0.0,
            $groups ?? []
        );
    }
}
