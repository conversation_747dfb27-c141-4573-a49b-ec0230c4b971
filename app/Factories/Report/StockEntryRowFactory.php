<?php

namespace App\Factories\Report;

use App\Domains\Inventory\Report\StockEntryRow;
use App\Models\StockEntry;

class StockEntryRowFactory
{

    public function buildFromStockEntry(StockEntry $stockEntry) : StockEntryRow {
        return new StockEntryRow(
            $stockEntry->user->first_name . " " . $stockEntry->user->last_name ?? null,
            $stockEntry->product->name ?? null,
            $stockEntry->quantity ?? null,
            $stockEntry->value ?? null,
            $stockEntry->created_at->format("d/m/Y H:i:s") ?? null
        );
    }
}
