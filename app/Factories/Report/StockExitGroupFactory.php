<?php

namespace App\Factories\Report;

use App\Domains\Inventory\Report\StockExitGroup;
use Illuminate\Support\Collection;

class StockExitGroupFactory
{

    private StockExitRowFactory $stockExitRowFactory;

    public function __construct(
        StockExitRowFactory $stockExitRowFactory,
    ){
        $this->stockExitRowFactory = $stockExitRowFactory;
    }

    /**
     * @param Collection $exits
     * @return StockExitGroup[]
     */
    public function buildFromModels(Collection $exits, ?string $grouped_by) : array {
        return match ($grouped_by) {
            "products" => $this->buildFromModelsWithProductsGrouped($exits),
            "projects" => $this->buildFromModelsWithProjectsGrouped($exits),
            "clients" => $this->buildFromModelsWitClientsGrouped($exits),
            "users" => $this->buildFromModelsWitUsersGrouped($exits),
            default => $this->buildFromModelsUngrouped($exits),
        };
    }

    public function buildFromModelsWithProductsGrouped(Collection $exits) : array {
        $groups = [];
        $stockGroups = [];

        foreach ($exits as $model){
            if(empty($groups[$model->product->name]["total_quantity"])) { $groups[$model->product->name]["total_quantity"] = 0; }
            if(empty($groups[$model->product->name]["total_value"])) { $groups[$model->product->name]["total_value"] = 0; }

            $groups[$model->product->name]["total_quantity"] += $model->quantity;
            $groups[$model->product->name]["total_value"] += $model->value;
            $groups[$model->product->name]["stock_exits"][] = $this->stockExitRowFactory->buildFromStockExit($model);
        }

        foreach ($groups as $name => $group){
            $stockGroups[] = new StockExitGroup(
                $name ?? null,
                $group["total_quantity"] ?? 0,
                $group["total_value"] ?? 0.00,
                $group["stock_exits"] ?? []
            );
        }
        return $stockGroups;
    }
    public function buildFromModelsWithProjectsGrouped(Collection $exits) : array {
        $groups = [];
        $stockGroups = [];

        foreach ($exits as $model){
            if(empty($groups[$model->project->name]["total_quantity"])) { $groups[$model->project->name]["total_quantity"] = 0; }
            if(empty($groups[$model->project->name]["total_value"])) { $groups[$model->project->name]["total_value"] = 0; }

            $groups[$model->project->name]["total_quantity"] += $model->quantity;
            $groups[$model->project->name]["total_value"] += $model->value;
            $groups[$model->project->name]["stock_exits"][] = $this->stockExitRowFactory->buildFromStockExit($model);
        }

        foreach ($groups as $name => $group){
            $stockGroups[] = new StockExitGroup(
                $name ?? null,
                $group["total_quantity"] ?? 0,
                $group["total_value"] ?? 0.00,
                $group["stock_exits"] ?? []
            );
        }
        return $stockGroups;
    }
    public function buildFromModelsWitClientsGrouped(Collection $exits) : array {
        $groups = [];
        $stockGroups = [];

        foreach ($exits as $model){
            if(empty($groups[$model->client->name]["total_quantity"])) { $groups[$model->client->name]["total_quantity"] = 0; }
            if(empty($groups[$model->client->name]["total_value"])) { $groups[$model->client->name]["total_value"] = 0; }

            $groups[$model->client->name]["total_quantity"] += $model->quantity;
            $groups[$model->client->name]["total_value"] += $model->value;
            $groups[$model->client->name]["stock_exits"][] = $this->stockExitRowFactory->buildFromStockExit($model);
        }

        foreach ($groups as $name => $group){
            $stockGroups[] = new StockExitGroup(
                $name ?? null,
                $group["total_quantity"] ?? 0,
                $group["total_value"] ?? 0.00,
                $group["stock_exits"] ?? []
            );
        }
        return $stockGroups;
    }
    public function buildFromModelsWitUsersGrouped(Collection $exits) : array {
        $groups = [];
        $stockGroups = [];

        foreach ($exits as $model){
            $key = $model->user->first_name . " " . $model->user->last_name;
            if(empty($groups[$key]["total_quantity"])) { $groups[$key]["total_quantity"] = 0; }
            if(empty($groups[$key]["total_value"])) { $groups[$key]["total_value"] = 0; }

            $groups[$key]["total_quantity"] += $model->quantity;
            $groups[$key]["total_value"] += $model->value;
            $groups[$key]["stock_exits"][] = $this->stockExitRowFactory->buildFromStockExit($model);
        }

        foreach ($groups as $name => $group){
            $stockGroups[] = new StockExitGroup(
                $name ?? null,
                $group["total_quantity"] ?? 0,
                $group["total_value"] ?? 0.00,
                $group["stock_exits"] ?? []
            );
        }
        return $stockGroups;
    }

    public function buildFromModelsUngrouped(Collection $exits) : array {
        $group = [];

        foreach ($exits as $model){
            if(empty($group["total_quantity"])) { $group["total_quantity"] = 0; }
            if(empty($group["total_value"])) { $group["total_value"] = 0; }

            $group["total_quantity"] += $model->quantity;
            $group["total_value"] += $model->value;
            $group["stock_exits"][] = $this->stockExitRowFactory->buildFromStockExit($model);
        }

        return [new StockExitGroup(
            null,
            $group["total_quantity"] ?? 0,
            $group["total_value"] ?? 0.00,
            $group["stock_exits"] ?? []
        )];
    }
}
