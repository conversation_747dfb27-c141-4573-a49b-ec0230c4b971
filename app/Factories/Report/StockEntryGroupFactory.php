<?php

namespace App\Factories\Report;

use App\Domains\Inventory\Report\StockEntryGroup;
use Illuminate\Support\Collection;

class StockEntryGroupFactory
{

    private StockEntryRowFactory $stockEntryRowFactory;

    public function __construct(
        StockEntryRowFactory $stockEntryRowFactory,
    ){
        $this->stockEntryRowFactory = $stockEntryRowFactory;
    }

    /**
     * @param Collection $entries
     * @return StockEntryGroup[]
     */
    public function buildFromModels(Collection $entries, ?string $grouped_by) : array {
        return match ($grouped_by) {
            "products" => $this->buildFromModelsWithProductsGrouped($entries),
            "users" => $this->buildFromModelsWithUsersGrouped($entries),
            default => $this->buildFromModelsUngrouped($entries),
        };
    }

    public function buildFromModelsWithProductsGrouped(Collection $entries) : array {
        $groups = [];
        $stockGroups = [];

        foreach ($entries as $model){
            if(empty($groups[$model->product->name]["total_quantity"])) { $groups[$model->product->name]["total_quantity"] = 0; }
            if(empty($groups[$model->product->name]["total_value"])) { $groups[$model->product->name]["total_value"] = 0; }

            $groups[$model->product->name]["total_quantity"] += $model->quantity;
            $groups[$model->product->name]["total_value"] += $model->value;
            $groups[$model->product->name]["stock_entries"][] = $this->stockEntryRowFactory->buildFromStockEntry($model);
        }

        foreach ($groups as $name => $group){
            $stockGroups[] = new StockEntryGroup(
                $name ?? null,
                $group["total_quantity"] ?? 0,
                $group["total_value"] ?? 0.00,
                $group["stock_entries"] ?? []
            );
        }
        return $stockGroups;
    }
    public function buildFromModelsWithUsersGrouped(Collection $entries) : array {
        $groups = [];
        $stockGroups = [];

        foreach ($entries as $model){
            $key = $model->user->first_name . " " . $model->user->last_name;
            if(empty($groups[$key]["total_quantity"])) { $groups[$key]["total_quantity"] = 0; }
            if(empty($groups[$key]["total_value"])) { $groups[$key]["total_value"] = 0; }

            $groups[$key]["total_quantity"] += $model->quantity;
            $groups[$key]["total_value"] += $model->value;
            $groups[$key]["stock_entries"][] = $this->stockEntryRowFactory->buildFromStockEntry($model);
        }

        foreach ($groups as $name => $group){
            $stockGroups[] = new StockEntryGroup(
                $name ?? null,
                $group["total_quantity"] ?? 0,
                $group["total_value"] ?? 0.00,
                $group["stock_entries"] ?? []
            );
        }
        return $stockGroups;
    }

    public function buildFromModelsUngrouped(Collection $entries) : array {
        $group = [];

        foreach ($entries as $model){
            if(empty($group["total_quantity"])) { $group["total_quantity"] = 0; }
            if(empty($group["total_value"])) { $group["total_value"] = 0; }

            $group["total_quantity"] += $model->quantity;
            $group["total_value"] += $model->value;
            $group["stock_entries"][] = $this->stockEntryRowFactory->buildFromStockEntry($model);
        }

        return [new StockEntryGroup(
            null,
            $group["total_quantity"] ?? 0,
            $group["total_value"] ?? 0.00,
            $group["stock_entries"] ?? []
        )];
    }
}
