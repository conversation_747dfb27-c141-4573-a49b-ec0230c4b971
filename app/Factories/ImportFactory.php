<?php

namespace App\Factories;

use App\Domains\Imports\Import;
use App\Helpers\File;
use App\Http\Requests\Import\StoreRequest;
use App\Http\Requests\Import\UpdateRequest;
use App\Models\Import as ImportModel;

class ImportFactory
{

    public function buildFromFile(File $file, StoreRequest $request) : Import {
        return new Import(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->model ?? null,
            $request->status ?? null,
            $request->header ?? null,
            $request->map ?? null,
            $request->is_processed ?? false,
            null,
            null,
            null,
            null,
            null,
            null,
            $file
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Import {
        return new Import(
            null,
            null,
            null,
            $request->model ?? null,
            $request->status ?? null,
            null,
            is_array($request->map ?? null) ? json_encode($request->map) : ($request->map ?? null),
            $request->is_processed ?? false,
            null,
            null,
            null,
            null,
            null,
            null,
        );
    }

    public function buildFromModel(?ImportModel $import) : ?Import {
        if(!$import){ return null; }


        return new Import(
            $import->id ?? null,
            $import->organization_id ?? null,
            $import->user_id ?? null,
            $import->model ?? null,
            $import->status ?? null,
            $import->header ?? null,
            $import->map ?? null,
            $import->is_processed ?? false,
            $import->file ?? "",
            $import->filename ?? "",
            $import->filepath ?? "",
            $import->filesize ?? "",
            $import->file_extension ?? "",
            $import->file_mime_type ?? "",
        );
    }
}
